---
tags:
  - 操作系统/windows/日志分析
cssclasses:
  - editor-full
适配系统: 
created: 2024-09-09T20:58
updated: 2025-07-03T14:45
---
# 简介
* 一个强大的命令行工具，用于管理和操作Windows事件日志


---

# 基础参数

|   参数    |         全称         |      描述      |
| :-----: | :----------------: | :----------: |
| **el**  |     enum-logs      | 列出系统中所有日志名称。 |
| **gl**  |      get-log       |   获取日志的属性。   |
| **sl**  |      set-log       |   设置日志的属性。   |
| **cl**  |     clear-log      |    清除日志。     |
| **epl** |     export-log     |    导出日志。     |
| **im**  |  install-manifest  | 安装事件日志的提供程序。 |
| **um**  | uninstall-manifest | 卸载事件日志的提供程序。 |
| **qe**  |    query-events    |   查询事件日志。    |
| **gli** |    get-log-info    | 获取日志提供程序的属性。 |
| **gp**  |    get-provider    |  获取提供程序的属性。  |

---
# 常见用法

## 查询特定事件ID并导出
```POWERSHELL
wevtutil qe System /q:"*[System/EventID=6005]" /f:text > C:\event6005.txt
```
* 导出系统日志中事件ID为6005的事件到C盘的event6005.txt文件

---
## 导出日志为XML格式
```powershell
wevtutil qe Security /f:xml /rd:true > c:\1.xml
```

---
## 清除日志事件 (管理员权限)
```POWERSHELL
wevtutil el 列出系统中所有日志名称  
wevtutil cl system 清理系统日志  
wevtutil cl application 清理应用程序日志  
wevtutil cl security 清理安全日志
```