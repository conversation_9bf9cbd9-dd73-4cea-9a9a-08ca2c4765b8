"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07450166,-0.03495057,-0.03405895,-0.02446091,-0.01865156,-0.01096582,-0.00166227,0.07995284,0.02001286,0.02093603,-0.00972963,-0.06140173,0.00547407,0.07460892,0.03024216,0.01378669,-0.02756703,0.0158253,-0.03588254,0.01279175,0.09536456,-0.07970043,-0.0370301,-0.05773336,-0.00678765,0.03490129,0.0157279,-0.01314243,-0.01264871,-0.18198158,0.0071888,0.02389927,0.01234409,0.02742112,-0.04693456,0.00558639,0.00193297,0.06929857,-0.02137026,0.02956905,-0.02384312,-0.00484235,0.05129287,-0.05215922,-0.00202632,-0.06046777,-0.0490422,0.01449968,-0.01237571,-0.02059644,-0.0583775,-0.06473307,-0.01313363,-0.04577426,-0.04207521,0.01363497,0.00232293,-0.00920943,0.0461334,-0.0272276,0.04162581,-0.00393997,-0.21299626,0.04340908,-0.00754671,-0.00177014,-0.03025473,-0.00913659,0.04961087,0.00001744,-0.06557976,-0.00238253,0.02494923,0.05147175,0.09918231,0.00527598,0.02938349,-0.04417753,-0.04425718,-0.02606334,-0.01410583,0.03622505,-0.01631337,-0.02671706,0.05181874,0.02930259,-0.00034477,-0.02766262,-0.00691553,-0.0240803,-0.02640199,-0.06038623,-0.01764734,0.04398267,-0.00052037,0.0176422,0.04901426,0.00809709,-0.03630032,0.14233603,-0.0558286,0.02891549,0.00421593,-0.01991176,0.03002156,-0.05632639,-0.00080484,-0.05180736,-0.02865047,-0.04145862,-0.07677219,-0.04933649,0.03618525,0.0092963,0.00196838,0.05639815,0.03938149,0.01209669,-0.02109023,-0.00342775,-0.00450745,-0.0019855,0.05845161,-0.05844773,-0.02121967,-0.04728249,0.06480641,0.084884,0.01902189,-0.00680515,0.081467,-0.04791486,-0.0758635,0.00709304,-0.00718583,-0.0191664,-0.0225244,-0.00846702,-0.00921994,-0.02448438,0.0136877,-0.07676937,0.00245018,-0.10019108,-0.06227005,0.02987182,-0.06337056,0.03709692,0.03297165,-0.00272448,0.00651645,0.06871155,0.00264064,-0.0375396,0.00829278,0.02079675,0.0390034,0.12808511,-0.03915284,-0.00000539,0.00795552,0.05672128,-0.02489812,0.14115235,0.00648416,-0.03528582,0.02581386,0.01278928,0.00550033,-0.01672267,0.01519089,0.01985832,0.01007105,0.04167394,0.09062164,0.00750724,-0.00463469,-0.02073112,0.02652456,0.04976512,0.05492219,-0.08293237,-0.03723779,0.05848868,0.05328937,-0.11127526,-0.02600053,-0.03741297,0.00163908,-0.06600615,-0.01312777,-0.00970775,0.00474695,0.04627453,-0.06339209,-0.09579472,0.03557216,-0.00952826,0.01411715,-0.02057807,0.05171516,0.00302774,-0.03129452,0.01016598,0.02389083,-0.00121881,0.02898245,-0.03298745,0.03500528,0.006895,-0.00852695,0.00146113,-0.0270469,0.02515589,-0.00277251,0.06976882,0.02893639,0.02934273,0.02994797,0.04649924,0.02295242,-0.03526773,-0.08171577,-0.23134893,-0.0718278,0.04095609,-0.05047815,-0.01392763,-0.00590856,0.01124005,-0.01787088,0.07882015,0.05528785,0.08602806,0.02678065,-0.05155928,-0.03447713,0.00558188,0.02001665,0.02159973,-0.0231702,-0.01104277,0.04720677,-0.02861683,0.05663925,-0.0060413,-0.02718186,0.02486889,-0.0289531,0.16117163,0.05675728,0.0068062,0.06693974,0.01620836,-0.01985781,-0.04699687,-0.05345706,0.01220495,0.06104636,-0.05376124,-0.04053393,-0.01417406,-0.04022883,-0.05708405,0.02990352,-0.06184975,-0.07820277,-0.05543101,-0.031757,-0.07375068,0.04391378,0.01394602,0.06319535,-0.02757934,0.0567955,0.00629492,-0.01723554,0.01674221,-0.01331357,-0.08104119,-0.05491076,-0.05694184,0.02977519,-0.00009775,-0.00004323,-0.01136173,0.07374387,0.00269163,-0.02483507,-0.02748808,0.00139547,-0.03211673,0.03687109,-0.03789047,0.1197725,0.01606084,-0.04888389,0.08566518,-0.00672538,-0.0341633,-0.06107002,-0.00876259,0.01010224,0.04923642,0.0111258,0.00801343,0.05882462,0.00726324,0.03832177,0.01867936,-0.00544876,0.01121237,-0.03928065,-0.04657065,0.03700816,-0.06468078,0.06116854,0.01017063,0.02529797,-0.29331338,0.04163282,-0.024328,0.03239002,0.06951085,0.02796652,0.06651653,0.02264455,-0.03473301,0.04290507,-0.07789124,0.03996022,0.02331283,-0.02372922,0.01101338,-0.04186315,0.01392411,0.0217353,0.09238048,-0.02284044,0.02766582,0.09928737,0.21212441,-0.0142261,0.03621054,-0.00156589,0.02663247,0.07669231,0.05282328,0.04317505,0.0198732,-0.01235565,0.03088971,-0.05619394,0.0091538,0.0182718,-0.04045301,-0.00308662,0.03120231,-0.02038104,-0.04033414,0.04282124,-0.06358377,0.02645797,0.12816162,0.07114948,-0.00031776,-0.05289902,-0.03467422,0.06636517,-0.02518016,0.04947097,-0.02485272,-0.04067983,0.00842126,0.06258277,-0.00482549,-0.02495718,-0.02799464,-0.04722808,-0.00103655,0.01474248,0.05097113,0.112439,0.03788615],"last_embed":{"hash":"899c341ff9d726e16f54deae21ee0481c7218667fed9715980182f0c95fc50d3","tokens":434}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.06166551,-0.06294696,0.03359846,-0.02476178,-0.01500005,0.05326273,0.03144704,-0.01830543,-0.00707319,-0.07853036,0.03360463,0.04271815,-0.0033713,-0.06675276,0.03700942,-0.03449137,0.00576424,0.02432254,0.03419296,-0.02459852,-0.02722895,-0.00862717,-0.03816678,0.01724731,0.06170825,0.00297939,0.04627077,-0.05671845,0.03274995,0.02635169,0.04003692,-0.01074553,0.03267645,-0.04042435,-0.01663834,0.04194148,-0.00773775,-0.00254762,0.03941284,-0.01609039,-0.00049932,0.03173262,-0.03040268,0.00519176,-0.02113595,0.0326839,-0.00839304,-0.04761038,-0.00187005,-0.01919209,0.04688281,-0.06093733,0.01759475,0.02440482,-0.02129591,0.03666708,-0.00065994,-0.08973252,-0.02907359,-0.00979093,-0.0060058,-0.02272009,0.04864101,-0.0203689,0.03460546,0.00829152,-0.00863824,0.00562032,-0.01086437,-0.0328815,0.00484252,-0.05912793,0.0384375,0.00110704,-0.00121495,0.00765153,0.02598148,-0.00789536,-0.03274586,0.08272709,-0.04388728,0.00207256,-0.00319522,0.07954662,0.03957127,-0.01923946,0.04316144,-0.05106117,-0.04957213,0.00932338,0.04826013,-0.04207256,-0.05835364,0.02217741,0.04178758,-0.00282548,0.01514969,0.02323347,0.01022709,-0.09100611,0.0373408,0.00676787,-0.0714292,-0.007839,0.00984206,0.01951682,0.00987278,0.05493176,-0.02065566,-0.04301522,0.0072079,0.04208999,-0.06769828,-0.05443798,-0.06227984,0.01511811,0.00440951,-0.04826344,-0.03858812,0.00600521,0.09192494,0.05369553,-0.02451874,-0.05999642,-0.03701099,0.06135162,-0.00618791,-0.01854002,0.0058221,-0.03818834,-0.04957508,0.03715641,-0.08069739,-0.01072949,0.0565189,0.00218593,0.08300584,-0.01185005,-0.04540947,-0.0020287,0.04387119,-0.01745557,-0.01313272,0.00187886,-0.02577847,0.05853688,-0.02671212,-0.07580558,0.03293879,-0.00777621,0.07837434,0.02668042,-0.01316836,-0.02666079,-0.01218585,-0.02033898,0.08812079,0.04258649,-0.06636462,-0.0392243,0.00189341,0.00978507,-0.0240214,-0.01548215,0.00730403,-0.01791917,-0.02462636,-0.00842955,-0.03328214,-0.04986985,0.00435002,0.00365886,-0.01943092,-0.0012856,0.04224615,0.01020123,0.03073056,0.04746204,-0.06563435,0.01857808,0.04014581,-0.01635697,-0.0084497,0.0040394,0.03989991,0.03009264,-0.00722738,0.00575963,0.08255411,-0.02344324,0.01641234,0.01858771,0.02581239,-0.00202783,0.00160264,0.05263164,-0.03138153,0.01226998,0.00293051,0.01101245,-0.06198147,-0.02162948,-0.01428933,-0.08178794,0.04360782,-0.00923432,-0.03633418,-0.0341764,-0.00691143,0.08929322,-0.01766983,-0.00498041,-0.00393292,0.01552592,-0.04817416,0.01607806,0.00987612,0.07336234,-0.01686245,-0.00300742,-0.02941226,0.03328944,-0.07488433,-0.071962,-0.0195277,-0.02853809,0.00678287,0.01873414,0.04743221,0.02788611,0.05207749,0.09549225,-0.06065406,-0.05982402,-0.047314,0.02516222,0.00706459,0.0917433,-0.03597766,0.02971434,0.01505278,-0.02766986,-0.02129166,0.02531771,-0.02356249,-0.01042761,-0.02657321,-0.01108047,0.03362978,0.04126635,0.05524288,0.00368619,0.02802536,0.03831584,0.02102382,-0.01338708,-0.00366487,0.03392226,-0.0311227,-0.04806076,0.02932446,-0.07994261,-0.03502979,0.04422608,-0.01166228,-0.00178288,-0.02565871,-0.01100029,-0.06450419,0.06054114,0.03164451,-0.01234169,-0.04953116,0.0497297,0.03324258,0.03718593,0.02273485,-0.02092901,-0.00515704,0.00215198,0.00703461,-0.03449478,0.01807759,-0.02796511,-0.03500867,0.02404105,-0.01940669,-0.03758893,0.03147455,0.05311171,0.00873035,-0.00298371,-0.05430429,0.0043654,-0.02182974,-0.01228928,0.07160192,-0.02372491,0.00332855,-0.06072857,0.0038909,-0.03136096,0.00899399,-0.03938863,0.01469579,0.05390405,0.00238586,-0.07156297,-0.05849969,-0.023851,-0.05382064,-0.04675292,0.004326,-0.01347304,0.01422121,0.04746523,0.0102262,0.01752551,-0.02050425,-0.07058329,-0.00802251,-0.007113,-0.03264708,0.03937381,-0.01603893,-0.09050824,-0.02249326,0.03886861,0.03109152,0.06451672,-0.00460874,0.01084571,-0.04393723,-0.00548527,0.02055101,0.02540028,-0.01225721,-0.07357647,0.01547027,-0.05942094,0.01844795,-0.04109486,0.00882944,-0.0213454,-0.0366041,0.03918746,0.01760323,-0.00742332,0.00301656,-0.04184327,0.01211235,0.01039747,-0.02442488,-0.01954969,-0.03785246,0.06619304,-0.00598548,-0.02904019,-0.02174711,0.02846202,0.05447275,0.00076058,0.06158972,-0.00854103,-0.04099723,0.00632942,0.02884596,0.0632452,-0.0043643,-0.02034492,-0.02419451,-0.00775903,-0.02466422,-0.0267182,0.04879931,-0.00982398,-0.01219442,0.04927001,0.02336447,0.00754383,-0.01915475,0.01417708,-0.02755997,-0.0265648,-0.07085495,-0.01496417,-0.06512921,-0.00062591,-0.00287727,-0.05713201,0.0423312,0.01212531,-0.05319212,-0.03930864,0.0313964,0.01051473,0.02886022,0.01916727,-0.00519457,0.02522929,0.08440183,-0.02519822,0.02226015,0.02225833,-0.0367541,0.07069466,-0.04472289,0.05683922,-0.0187125,-0.05473293,-0.01468289,0.0211436,-0.03662448,0.00663073,-0.03450029,-0.02561424,0.01265736,-0.05551329,-0.02236477,0.00151306,-0.00921606,-0.00626515,0.01502823,0.0562261,-0.03091619,-0.00906346,-0.01619149,-0.04900637,0.03360537,-0.03106506,-0.01226922,-0.02330925,-0.10255034,-0.01346287,-0.01996896,-0.00905725,0.04112397,-0.00096662,0.01973051,0.01034708,-0.01466922,-0.01563211,0.0407119,-0.0283324,-0.00228563,-0.02191325,-0.00583333,-0.06369161,-0.01048595,0.04496069,0.00568576,-0.00848872,-0.06520494,0.03539803,0.03758086,-0.00747171,-0.03817672,0.03464342,0.03906904,0.05585684,-0.10845045,0.00297284,0.01424847,0.01488873,0.01591931,0.03674264,0.0121776,-0.01992015,-0.03621693,-0.00428197,-0.04128969,-0.01487484,0.0450149,-0.04962481,0.09504475,-0.00832792,0.01244254,0.0533105,-0.04449505,0.01573513,0.01472287,-0.00516597,-0.05945299,-0.02346274,-0.02725114,0.01052432,0.00091019,-0.01236533,0.01971176,-0.03346375,0.02554168,0.00106954,0.00136396,-0.01830938,-0.05014367,0.02646412,0.01583599,0.05632435,-0.01363652,0.04552497,-0.04598301,0.05073087,-0.03166187,0.03601352,0.03341631,-0.00125964,-0.03857197,0.01865239,0.00339032,-0.06689212,0.0421943,-0.03019467,0.07607831,0.00160691,0.00175514,-0.00493718,0.02921489,0.04809036,0.03688014,0.00137595,0.0568455,0.02758082,-0.00457222,-0.0013826,0.02143729,-0.01880583,-0.02615364,-0.01841469,0.00914793,-0.01395869,-0.01693975,0.01232238,0.03057889,-0.02073092,0.01517743,-0.02213262,0.03631119,-0.04783166,0.00729491,0.04712943,0.03980743,-0.00021029,0.0215779,0.006532,-0.00693208,0.02557934,0.01580697,-0.01250818,-0.00417,-0.0253603,0.01208963,-0.02542991,-0.00407541,-0.03305207,0.01510319,0.05091275,0.03966154,-0.06640855,-0.00803004,0.03874455,0.05101617,0.06334053,0.01798928,-0.025812,-0.03534263,0.00839504,-0.0712096,0.026663,0.00235547,-0.02273028,-0.1106445,-0.00678745,0.05246691,0.0536608,-0.04952858,0.02230135,-0.02273481,-0.021791,0.05203844,0.02015911,0.03078189,0.02018761,0.07363094,-0.02949084,0.00223779,-0.02107723,-0.0236122,0.04298153,0.03946525,-0.03445082,0.04836597,-0.02403358,-0.05459739,-0.01147115,-0.0255148,0.02220332,-0.02816501,0.01801587,-0.00052898,-0.01824679,0.03078111,-0.00869478,-0.04640202,-0.02792106,-0.00089534,-0.03977945,-0.01451935,-0.00999242,0.00346171,0.02301392,0.02330367,0.04133996,-0.00397493,-0.04258334,0.03018633,-0.01873322,0.02019036,0.03610135,-0.01055137,0.03630768,-0.03316218,-0.00524372,-0.06221643,-0.00617374,-0.04390145,0.02178605,0.02021676,0.03443513,0.01526501,-0.0133716,-0.00153394,-0.03376181,-0.0200574,0.01734577,0.04908661,0.00576185,-0.02376673,-0.01581087,0.02133504,0.04356733,-0.00100683,-0.04002697,0.06406566,0.01241226,0.02990908,-0.02226071,-0.0439675,-0.00263276,-0.02321459,-0.04052983,0.04834206,-0.01943845,0.05343823,0.00109821,-0.00858303,0.04569134,0.02724847,-0.03605388,0.02994293,0.07587915,-0.0159915,-0.01188841,0.05252726,0.04770757,0.04169229,0.02518262,0.06824961,-0.00798006,-0.043754,0.04680116,0.02300445,-0.03085474,0.02594341,0.01797996,0.00550848,0.02320509,0.01518365,0.04816358,0.0344325,-0.03647599,-0.02633588,0.00606028,0.02235316,0.00799256,0.01410528,-0.04943022,0.0625892,0.00254131,-0.02085764,0.00032753,-0.07154642,0.015109,0.0017895,0.01034436,0.0207756,-0.05811686,-0.01174381,0.01092645,0.01514457,-0.02180099,-0.02112495,-0.00038802,-0.03891915,0.00971843,0.00664354,-0.00608524,0.04668708,0.02781872,0.00009537,0.02457976,0.01747745,-0.01851362,0.0195659,0.01273507,0.04200809,-0.04395432,-0.05559657,0.05047225,-0.04000702,-0.05156279,0.00525247,0.02075072,0.05553332,0.04781377,-0.03971458,-0.00784189,0.01355642,-0.00703366,-0.04985132,0.01176185,0.03130983,-0.09489752,0.00778122,0.04999823,-0.01105797,0.01941856,0.02328304,-0.03089191,0.02279783,-0.02687363,-0.02678579,-0.00328758,-0.00459304,-0.07334253,0.03217085,-0.03073187,0.0131143,-0.00210671,-0.00622868,0.01601563,-0.0573914,-0.02400116,-0.05583375,0.08911359,0.01409569,-0.01577925,-0.02797735,-0.02173988,8.3e-7,-0.07924049,0.01222306,0.03648077,-0.03110208,-0.02618023,-0.04605546,-0.09274605,-0.00704618,0.02717767],"last_embed":{"tokens":371,"hash":"rqn3zu"}}},"last_read":{"hash":"rqn3zu","at":1751422354210},"class_name":"SmartSource","outlinks":[{"title":"防火墙","target":"防火墙","line":37},{"title":"Knock","target":"Knock","line":50}],"blocks":{"#---frontmatter---":[1,4],"#实现原理":[6,47],"#实现原理#流程可视化":[8,47],"#实现原理#流程可视化#{1}":[9,34],"#实现原理#流程可视化#{2}":[35,35],"#实现原理#流程可视化#{3}":[36,37],"#实现原理#流程可视化#{4}":[38,38],"#实现原理#流程可视化#{5}":[39,41],"#实现原理#流程可视化#{6}":[42,47],"#相关的工具":[48,64],"#相关的工具#[[Knock]]":[50,53],"#相关的工具#[[Knock]]#{1}":[51,51],"#相关的工具#[[Knock]]#{2}":[52,53],"#相关的工具#存在的问题":[54,64],"#相关的工具#存在的问题#{1}":[55,58],"#相关的工具#存在的问题#{2}":[59,64]},"last_import":{"mtime":1748684057154,"size":1639,"at":1748744168188,"hash":"rqn3zu"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md","metadata":{"cssclasses":["editor-full"]},"last_embed":{"hash":"rqn3zu","at":1751422354210}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#---frontmatter---","lines":[1,4],"size":35,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#实现原理": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#实现原理","lines":[6,47],"size":636,"outlinks":[{"title":"防火墙","target":"防火墙","line":32}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#实现原理#流程可视化": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#实现原理#流程可视化","lines":[8,47],"size":628,"outlinks":[{"title":"防火墙","target":"防火墙","line":30}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#实现原理#流程可视化#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#实现原理#流程可视化#{1}","lines":[9,34],"size":440,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#实现原理#流程可视化#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#实现原理#流程可视化#{2}","lines":[35,35],"size":23,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#实现原理#流程可视化#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#实现原理#流程可视化#{3}","lines":[36,37],"size":56,"outlinks":[{"title":"防火墙","target":"防火墙","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#实现原理#流程可视化#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#实现原理#流程可视化#{4}","lines":[38,38],"size":18,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#实现原理#流程可视化#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#实现原理#流程可视化#{5}","lines":[39,41],"size":66,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#实现原理#流程可视化#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#实现原理#流程可视化#{6}","lines":[42,47],"size":11,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#相关的工具": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#相关的工具","lines":[48,64],"size":191,"outlinks":[{"title":"Knock","target":"Knock","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#相关的工具#[[Knock]]": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#相关的工具#[[Knock]]","lines":[50,53],"size":49,"outlinks":[{"title":"Knock","target":"Knock","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#相关的工具#[[Knock]]#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#相关的工具#[[Knock]]#{1}","lines":[51,51],"size":15,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#相关的工具#[[Knock]]#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#相关的工具#[[Knock]]#{2}","lines":[52,53],"size":20,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#相关的工具#存在的问题": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#相关的工具#存在的问题","lines":[54,64],"size":132,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#相关的工具#存在的问题#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#相关的工具#存在的问题#{1}","lines":[55,58],"size":110,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#相关的工具#存在的问题#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md#相关的工具#存在的问题#{2}","lines":[59,64],"size":11,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07450166,-0.03495057,-0.03405895,-0.02446091,-0.01865156,-0.01096582,-0.00166227,0.07995284,0.02001286,0.02093603,-0.00972963,-0.06140173,0.00547407,0.07460892,0.03024216,0.01378669,-0.02756703,0.0158253,-0.03588254,0.01279175,0.09536456,-0.07970043,-0.0370301,-0.05773336,-0.00678765,0.03490129,0.0157279,-0.01314243,-0.01264871,-0.18198158,0.0071888,0.02389927,0.01234409,0.02742112,-0.04693456,0.00558639,0.00193297,0.06929857,-0.02137026,0.02956905,-0.02384312,-0.00484235,0.05129287,-0.05215922,-0.00202632,-0.06046777,-0.0490422,0.01449968,-0.01237571,-0.02059644,-0.0583775,-0.06473307,-0.01313363,-0.04577426,-0.04207521,0.01363497,0.00232293,-0.00920943,0.0461334,-0.0272276,0.04162581,-0.00393997,-0.21299626,0.04340908,-0.00754671,-0.00177014,-0.03025473,-0.00913659,0.04961087,0.00001744,-0.06557976,-0.00238253,0.02494923,0.05147175,0.09918231,0.00527598,0.02938349,-0.04417753,-0.04425718,-0.02606334,-0.01410583,0.03622505,-0.01631337,-0.02671706,0.05181874,0.02930259,-0.00034477,-0.02766262,-0.00691553,-0.0240803,-0.02640199,-0.06038623,-0.01764734,0.04398267,-0.00052037,0.0176422,0.04901426,0.00809709,-0.03630032,0.14233603,-0.0558286,0.02891549,0.00421593,-0.01991176,0.03002156,-0.05632639,-0.00080484,-0.05180736,-0.02865047,-0.04145862,-0.07677219,-0.04933649,0.03618525,0.0092963,0.00196838,0.05639815,0.03938149,0.01209669,-0.02109023,-0.00342775,-0.00450745,-0.0019855,0.05845161,-0.05844773,-0.02121967,-0.04728249,0.06480641,0.084884,0.01902189,-0.00680515,0.081467,-0.04791486,-0.0758635,0.00709304,-0.00718583,-0.0191664,-0.0225244,-0.00846702,-0.00921994,-0.02448438,0.0136877,-0.07676937,0.00245018,-0.10019108,-0.06227005,0.02987182,-0.06337056,0.03709692,0.03297165,-0.00272448,0.00651645,0.06871155,0.00264064,-0.0375396,0.00829278,0.02079675,0.0390034,0.12808511,-0.03915284,-0.00000539,0.00795552,0.05672128,-0.02489812,0.14115235,0.00648416,-0.03528582,0.02581386,0.01278928,0.00550033,-0.01672267,0.01519089,0.01985832,0.01007105,0.04167394,0.09062164,0.00750724,-0.00463469,-0.02073112,0.02652456,0.04976512,0.05492219,-0.08293237,-0.03723779,0.05848868,0.05328937,-0.11127526,-0.02600053,-0.03741297,0.00163908,-0.06600615,-0.01312777,-0.00970775,0.00474695,0.04627453,-0.06339209,-0.09579472,0.03557216,-0.00952826,0.01411715,-0.02057807,0.05171516,0.00302774,-0.03129452,0.01016598,0.02389083,-0.00121881,0.02898245,-0.03298745,0.03500528,0.006895,-0.00852695,0.00146113,-0.0270469,0.02515589,-0.00277251,0.06976882,0.02893639,0.02934273,0.02994797,0.04649924,0.02295242,-0.03526773,-0.08171577,-0.23134893,-0.0718278,0.04095609,-0.05047815,-0.01392763,-0.00590856,0.01124005,-0.01787088,0.07882015,0.05528785,0.08602806,0.02678065,-0.05155928,-0.03447713,0.00558188,0.02001665,0.02159973,-0.0231702,-0.01104277,0.04720677,-0.02861683,0.05663925,-0.0060413,-0.02718186,0.02486889,-0.0289531,0.16117163,0.05675728,0.0068062,0.06693974,0.01620836,-0.01985781,-0.04699687,-0.05345706,0.01220495,0.06104636,-0.05376124,-0.04053393,-0.01417406,-0.04022883,-0.05708405,0.02990352,-0.06184975,-0.07820277,-0.05543101,-0.031757,-0.07375068,0.04391378,0.01394602,0.06319535,-0.02757934,0.0567955,0.00629492,-0.01723554,0.01674221,-0.01331357,-0.08104119,-0.05491076,-0.05694184,0.02977519,-0.00009775,-0.00004323,-0.01136173,0.07374387,0.00269163,-0.02483507,-0.02748808,0.00139547,-0.03211673,0.03687109,-0.03789047,0.1197725,0.01606084,-0.04888389,0.08566518,-0.00672538,-0.0341633,-0.06107002,-0.00876259,0.01010224,0.04923642,0.0111258,0.00801343,0.05882462,0.00726324,0.03832177,0.01867936,-0.00544876,0.01121237,-0.03928065,-0.04657065,0.03700816,-0.06468078,0.06116854,0.01017063,0.02529797,-0.29331338,0.04163282,-0.024328,0.03239002,0.06951085,0.02796652,0.06651653,0.02264455,-0.03473301,0.04290507,-0.07789124,0.03996022,0.02331283,-0.02372922,0.01101338,-0.04186315,0.01392411,0.0217353,0.09238048,-0.02284044,0.02766582,0.09928737,0.21212441,-0.0142261,0.03621054,-0.00156589,0.02663247,0.07669231,0.05282328,0.04317505,0.0198732,-0.01235565,0.03088971,-0.05619394,0.0091538,0.0182718,-0.04045301,-0.00308662,0.03120231,-0.02038104,-0.04033414,0.04282124,-0.06358377,0.02645797,0.12816162,0.07114948,-0.00031776,-0.05289902,-0.03467422,0.06636517,-0.02518016,0.04947097,-0.02485272,-0.04067983,0.00842126,0.06258277,-0.00482549,-0.02495718,-0.02799464,-0.04722808,-0.00103655,0.01474248,0.05097113,0.112439,0.03788615],"last_embed":{"hash":"899c341ff9d726e16f54deae21ee0481c7218667fed9715980182f0c95fc50d3","tokens":434}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.06166551,-0.06294696,0.03359846,-0.02476178,-0.01500005,0.05326273,0.03144704,-0.01830543,-0.00707319,-0.07853036,0.03360463,0.04271815,-0.0033713,-0.06675276,0.03700942,-0.03449137,0.00576424,0.02432254,0.03419296,-0.02459852,-0.02722895,-0.00862717,-0.03816678,0.01724731,0.06170825,0.00297939,0.04627077,-0.05671845,0.03274995,0.02635169,0.04003692,-0.01074553,0.03267645,-0.04042435,-0.01663834,0.04194148,-0.00773775,-0.00254762,0.03941284,-0.01609039,-0.00049932,0.03173262,-0.03040268,0.00519176,-0.02113595,0.0326839,-0.00839304,-0.04761038,-0.00187005,-0.01919209,0.04688281,-0.06093733,0.01759475,0.02440482,-0.02129591,0.03666708,-0.00065994,-0.08973252,-0.02907359,-0.00979093,-0.0060058,-0.02272009,0.04864101,-0.0203689,0.03460546,0.00829152,-0.00863824,0.00562032,-0.01086437,-0.0328815,0.00484252,-0.05912793,0.0384375,0.00110704,-0.00121495,0.00765153,0.02598148,-0.00789536,-0.03274586,0.08272709,-0.04388728,0.00207256,-0.00319522,0.07954662,0.03957127,-0.01923946,0.04316144,-0.05106117,-0.04957213,0.00932338,0.04826013,-0.04207256,-0.05835364,0.02217741,0.04178758,-0.00282548,0.01514969,0.02323347,0.01022709,-0.09100611,0.0373408,0.00676787,-0.0714292,-0.007839,0.00984206,0.01951682,0.00987278,0.05493176,-0.02065566,-0.04301522,0.0072079,0.04208999,-0.06769828,-0.05443798,-0.06227984,0.01511811,0.00440951,-0.04826344,-0.03858812,0.00600521,0.09192494,0.05369553,-0.02451874,-0.05999642,-0.03701099,0.06135162,-0.00618791,-0.01854002,0.0058221,-0.03818834,-0.04957508,0.03715641,-0.08069739,-0.01072949,0.0565189,0.00218593,0.08300584,-0.01185005,-0.04540947,-0.0020287,0.04387119,-0.01745557,-0.01313272,0.00187886,-0.02577847,0.05853688,-0.02671212,-0.07580558,0.03293879,-0.00777621,0.07837434,0.02668042,-0.01316836,-0.02666079,-0.01218585,-0.02033898,0.08812079,0.04258649,-0.06636462,-0.0392243,0.00189341,0.00978507,-0.0240214,-0.01548215,0.00730403,-0.01791917,-0.02462636,-0.00842955,-0.03328214,-0.04986985,0.00435002,0.00365886,-0.01943092,-0.0012856,0.04224615,0.01020123,0.03073056,0.04746204,-0.06563435,0.01857808,0.04014581,-0.01635697,-0.0084497,0.0040394,0.03989991,0.03009264,-0.00722738,0.00575963,0.08255411,-0.02344324,0.01641234,0.01858771,0.02581239,-0.00202783,0.00160264,0.05263164,-0.03138153,0.01226998,0.00293051,0.01101245,-0.06198147,-0.02162948,-0.01428933,-0.08178794,0.04360782,-0.00923432,-0.03633418,-0.0341764,-0.00691143,0.08929322,-0.01766983,-0.00498041,-0.00393292,0.01552592,-0.04817416,0.01607806,0.00987612,0.07336234,-0.01686245,-0.00300742,-0.02941226,0.03328944,-0.07488433,-0.071962,-0.0195277,-0.02853809,0.00678287,0.01873414,0.04743221,0.02788611,0.05207749,0.09549225,-0.06065406,-0.05982402,-0.047314,0.02516222,0.00706459,0.0917433,-0.03597766,0.02971434,0.01505278,-0.02766986,-0.02129166,0.02531771,-0.02356249,-0.01042761,-0.02657321,-0.01108047,0.03362978,0.04126635,0.05524288,0.00368619,0.02802536,0.03831584,0.02102382,-0.01338708,-0.00366487,0.03392226,-0.0311227,-0.04806076,0.02932446,-0.07994261,-0.03502979,0.04422608,-0.01166228,-0.00178288,-0.02565871,-0.01100029,-0.06450419,0.06054114,0.03164451,-0.01234169,-0.04953116,0.0497297,0.03324258,0.03718593,0.02273485,-0.02092901,-0.00515704,0.00215198,0.00703461,-0.03449478,0.01807759,-0.02796511,-0.03500867,0.02404105,-0.01940669,-0.03758893,0.03147455,0.05311171,0.00873035,-0.00298371,-0.05430429,0.0043654,-0.02182974,-0.01228928,0.07160192,-0.02372491,0.00332855,-0.06072857,0.0038909,-0.03136096,0.00899399,-0.03938863,0.01469579,0.05390405,0.00238586,-0.07156297,-0.05849969,-0.023851,-0.05382064,-0.04675292,0.004326,-0.01347304,0.01422121,0.04746523,0.0102262,0.01752551,-0.02050425,-0.07058329,-0.00802251,-0.007113,-0.03264708,0.03937381,-0.01603893,-0.09050824,-0.02249326,0.03886861,0.03109152,0.06451672,-0.00460874,0.01084571,-0.04393723,-0.00548527,0.02055101,0.02540028,-0.01225721,-0.07357647,0.01547027,-0.05942094,0.01844795,-0.04109486,0.00882944,-0.0213454,-0.0366041,0.03918746,0.01760323,-0.00742332,0.00301656,-0.04184327,0.01211235,0.01039747,-0.02442488,-0.01954969,-0.03785246,0.06619304,-0.00598548,-0.02904019,-0.02174711,0.02846202,0.05447275,0.00076058,0.06158972,-0.00854103,-0.04099723,0.00632942,0.02884596,0.0632452,-0.0043643,-0.02034492,-0.02419451,-0.00775903,-0.02466422,-0.0267182,0.04879931,-0.00982398,-0.01219442,0.04927001,0.02336447,0.00754383,-0.01915475,0.01417708,-0.02755997,-0.0265648,-0.07085495,-0.01496417,-0.06512921,-0.00062591,-0.00287727,-0.05713201,0.0423312,0.01212531,-0.05319212,-0.03930864,0.0313964,0.01051473,0.02886022,0.01916727,-0.00519457,0.02522929,0.08440183,-0.02519822,0.02226015,0.02225833,-0.0367541,0.07069466,-0.04472289,0.05683922,-0.0187125,-0.05473293,-0.01468289,0.0211436,-0.03662448,0.00663073,-0.03450029,-0.02561424,0.01265736,-0.05551329,-0.02236477,0.00151306,-0.00921606,-0.00626515,0.01502823,0.0562261,-0.03091619,-0.00906346,-0.01619149,-0.04900637,0.03360537,-0.03106506,-0.01226922,-0.02330925,-0.10255034,-0.01346287,-0.01996896,-0.00905725,0.04112397,-0.00096662,0.01973051,0.01034708,-0.01466922,-0.01563211,0.0407119,-0.0283324,-0.00228563,-0.02191325,-0.00583333,-0.06369161,-0.01048595,0.04496069,0.00568576,-0.00848872,-0.06520494,0.03539803,0.03758086,-0.00747171,-0.03817672,0.03464342,0.03906904,0.05585684,-0.10845045,0.00297284,0.01424847,0.01488873,0.01591931,0.03674264,0.0121776,-0.01992015,-0.03621693,-0.00428197,-0.04128969,-0.01487484,0.0450149,-0.04962481,0.09504475,-0.00832792,0.01244254,0.0533105,-0.04449505,0.01573513,0.01472287,-0.00516597,-0.05945299,-0.02346274,-0.02725114,0.01052432,0.00091019,-0.01236533,0.01971176,-0.03346375,0.02554168,0.00106954,0.00136396,-0.01830938,-0.05014367,0.02646412,0.01583599,0.05632435,-0.01363652,0.04552497,-0.04598301,0.05073087,-0.03166187,0.03601352,0.03341631,-0.00125964,-0.03857197,0.01865239,0.00339032,-0.06689212,0.0421943,-0.03019467,0.07607831,0.00160691,0.00175514,-0.00493718,0.02921489,0.04809036,0.03688014,0.00137595,0.0568455,0.02758082,-0.00457222,-0.0013826,0.02143729,-0.01880583,-0.02615364,-0.01841469,0.00914793,-0.01395869,-0.01693975,0.01232238,0.03057889,-0.02073092,0.01517743,-0.02213262,0.03631119,-0.04783166,0.00729491,0.04712943,0.03980743,-0.00021029,0.0215779,0.006532,-0.00693208,0.02557934,0.01580697,-0.01250818,-0.00417,-0.0253603,0.01208963,-0.02542991,-0.00407541,-0.03305207,0.01510319,0.05091275,0.03966154,-0.06640855,-0.00803004,0.03874455,0.05101617,0.06334053,0.01798928,-0.025812,-0.03534263,0.00839504,-0.0712096,0.026663,0.00235547,-0.02273028,-0.1106445,-0.00678745,0.05246691,0.0536608,-0.04952858,0.02230135,-0.02273481,-0.021791,0.05203844,0.02015911,0.03078189,0.02018761,0.07363094,-0.02949084,0.00223779,-0.02107723,-0.0236122,0.04298153,0.03946525,-0.03445082,0.04836597,-0.02403358,-0.05459739,-0.01147115,-0.0255148,0.02220332,-0.02816501,0.01801587,-0.00052898,-0.01824679,0.03078111,-0.00869478,-0.04640202,-0.02792106,-0.00089534,-0.03977945,-0.01451935,-0.00999242,0.00346171,0.02301392,0.02330367,0.04133996,-0.00397493,-0.04258334,0.03018633,-0.01873322,0.02019036,0.03610135,-0.01055137,0.03630768,-0.03316218,-0.00524372,-0.06221643,-0.00617374,-0.04390145,0.02178605,0.02021676,0.03443513,0.01526501,-0.0133716,-0.00153394,-0.03376181,-0.0200574,0.01734577,0.04908661,0.00576185,-0.02376673,-0.01581087,0.02133504,0.04356733,-0.00100683,-0.04002697,0.06406566,0.01241226,0.02990908,-0.02226071,-0.0439675,-0.00263276,-0.02321459,-0.04052983,0.04834206,-0.01943845,0.05343823,0.00109821,-0.00858303,0.04569134,0.02724847,-0.03605388,0.02994293,0.07587915,-0.0159915,-0.01188841,0.05252726,0.04770757,0.04169229,0.02518262,0.06824961,-0.00798006,-0.043754,0.04680116,0.02300445,-0.03085474,0.02594341,0.01797996,0.00550848,0.02320509,0.01518365,0.04816358,0.0344325,-0.03647599,-0.02633588,0.00606028,0.02235316,0.00799256,0.01410528,-0.04943022,0.0625892,0.00254131,-0.02085764,0.00032753,-0.07154642,0.015109,0.0017895,0.01034436,0.0207756,-0.05811686,-0.01174381,0.01092645,0.01514457,-0.02180099,-0.02112495,-0.00038802,-0.03891915,0.00971843,0.00664354,-0.00608524,0.04668708,0.02781872,0.00009537,0.02457976,0.01747745,-0.01851362,0.0195659,0.01273507,0.04200809,-0.04395432,-0.05559657,0.05047225,-0.04000702,-0.05156279,0.00525247,0.02075072,0.05553332,0.04781377,-0.03971458,-0.00784189,0.01355642,-0.00703366,-0.04985132,0.01176185,0.03130983,-0.09489752,0.00778122,0.04999823,-0.01105797,0.01941856,0.02328304,-0.03089191,0.02279783,-0.02687363,-0.02678579,-0.00328758,-0.00459304,-0.07334253,0.03217085,-0.03073187,0.0131143,-0.00210671,-0.00622868,0.01601563,-0.0573914,-0.02400116,-0.05583375,0.08911359,0.01409569,-0.01577925,-0.02797735,-0.02173988,8.3e-7,-0.07924049,0.01222306,0.03648077,-0.03110208,-0.02618023,-0.04605546,-0.09274605,-0.00704618,0.02717767],"last_embed":{"tokens":371,"hash":"rqn3zu"}}},"last_read":{"hash":"rqn3zu","at":1751441877328},"class_name":"SmartSource","outlinks":[{"title":"防火墙","target":"防火墙","line":37},{"title":"Knock","target":"Knock","line":50}],"blocks":{"#---frontmatter---":[1,4],"#实现原理":[6,47],"#实现原理#流程可视化":[8,47],"#实现原理#流程可视化#{1}":[9,34],"#实现原理#流程可视化#{2}":[35,35],"#实现原理#流程可视化#{3}":[36,37],"#实现原理#流程可视化#{4}":[38,38],"#实现原理#流程可视化#{5}":[39,41],"#实现原理#流程可视化#{6}":[42,47],"#相关的工具":[48,64],"#相关的工具#[[Knock]]":[50,53],"#相关的工具#[[Knock]]#{1}":[51,51],"#相关的工具#[[Knock]]#{2}":[52,53],"#相关的工具#存在的问题":[54,64],"#相关的工具#存在的问题#{1}":[55,58],"#相关的工具#存在的问题#{2}":[59,64]},"last_import":{"mtime":1748684057154,"size":1639,"at":1748744168188,"hash":"rqn3zu"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术.md","metadata":{"cssclasses":["editor-full"]},"last_embed":{"hash":"rqn3zu","at":1751441877328}},