"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0862896,-0.05875936,0.02705097,-0.0194959,0.01845303,-0.00534725,0.00596357,0.02610843,0.0540526,-0.01708973,0.02673163,-0.0611427,0.03886777,0.00444548,0.01380941,0.05314081,0.01831845,0.03846177,0.02236341,-0.05161541,0.0618456,-0.01646807,0.00755886,-0.10545804,-0.00123357,0.06142835,-0.00035323,-0.01190198,0.02087358,-0.15614164,0.03101119,0.04294452,-0.04264385,0.01196328,0.02148816,-0.02939422,0.0059276,0.01774995,-0.03457314,0.04215825,0.03201216,-0.00084013,-0.02371047,-0.01391272,-0.01137237,-0.04882977,-0.01780758,-0.00614633,0.00935858,-0.07009139,-0.01787374,-0.04605598,-0.01336714,-0.06837823,-0.01329588,0.0521067,0.04271741,0.00554508,0.06246879,-0.10493853,0.03734134,0.02735208,-0.22715035,0.07005568,0.07135711,-0.00860706,0.00261165,0.00040874,0.0169422,0.05388031,-0.08257305,0.05254437,-0.02119097,0.05273438,0.06636799,-0.02627828,-0.01251822,-0.02169417,-0.0049252,0.0020416,-0.01317066,0.00849941,-0.01922498,0.00144471,-0.03418099,0.01068219,-0.00000984,0.00233698,-0.0001658,-0.00975036,-0.02341319,-0.05361446,0.04403425,0.04110833,-0.02292982,0.00713573,0.01592848,0.05181104,-0.12741132,0.08637661,-0.04650898,0.02960466,-0.0168019,-0.05727071,0.04605373,-0.05896758,-0.03021462,-0.02509686,-0.01287543,-0.00405114,-0.03769906,-0.02709853,0.05307391,-0.0514693,0.04146672,0.02480081,-0.00216448,0.00562806,-0.05536664,-0.00118096,-0.02884067,0.00257588,0.03841712,-0.01271763,-0.02683817,-0.06025382,0.06239465,0.07649897,-0.00679008,0.01443827,0.03728394,-0.03453914,0.01624791,0.01096589,0.03918225,0.04128223,0.01467713,-0.05328878,-0.01696248,-0.05821094,0.01954918,-0.0828558,0.01285863,-0.09527571,-0.0584297,0.06072983,-0.06266388,0.04420226,0.04767064,-0.06264956,-0.02160763,0.02087115,-0.00400823,-0.05731723,0.00988147,0.01446577,0.10581618,0.15858722,-0.04367861,-0.0285296,-0.06502442,0.00409121,-0.09405357,0.13084573,0.02685213,-0.05476625,-0.04252555,0.00027149,-0.02071943,-0.02709553,-0.01515777,-0.04631064,0.02095316,-0.02759256,0.07617763,-0.00670676,-0.03339579,-0.04055701,0.02000398,0.02170761,0.02955176,-0.02342081,-0.03995081,-0.01691783,-0.01367986,-0.0718834,-0.06211292,-0.04410197,-0.00859753,-0.05947896,-0.11968724,0.0048841,0.05058772,0.01463625,-0.02708683,-0.06135388,0.02475387,0.01754727,0.09035686,-0.03961388,0.10675166,0.02330087,0.01508561,0.02035749,-0.06803694,-0.00385227,-0.02017314,0.00442902,0.03464985,-0.02680613,-0.0342884,0.02375884,-0.01215585,0.00943625,-0.01023699,0.05325721,-0.01230456,0.0100225,0.00118362,0.05607568,0.08500579,0.0151691,-0.06581437,-0.20708567,-0.04484741,0.00370624,-0.04244376,0.0256812,-0.01805873,0.0276972,0.00894846,0.00932198,0.02653795,0.04712673,-0.01694481,-0.04060037,0.01571704,-0.00538607,-0.01772507,0.0682827,-0.01865274,-0.01226498,0.00258406,-0.03158837,0.0741692,-0.05910929,-0.05196114,0.04978632,0.02479215,0.14090347,-0.00942031,0.05223808,0.02302555,0.03929453,0.07491145,0.01587285,-0.11653672,0.00613262,0.0814138,0.04161761,-0.0307142,0.01152892,0.05153428,-0.01502321,0.04809168,-0.03166154,-0.08013038,-0.03344918,-0.05489213,0.0005923,-0.03311672,-0.03594155,-0.00771691,-0.02324867,-0.02777659,-0.00502742,0.00858965,0.00024777,-0.04166688,-0.04852812,-0.0223332,-0.00386619,0.03057037,0.05461552,0.04137726,-0.0039857,0.03727289,0.05484701,-0.01539836,-0.0201231,0.03890949,0.02910476,-0.01555962,-0.02324613,0.12104082,0.03307879,0.00062701,0.06545887,-0.03190377,-0.0188074,-0.07678773,0.00878914,0.02188995,0.07879443,0.04203482,0.06635109,0.01751194,0.03306688,0.00523809,0.01111663,0.05101211,0.12381763,-0.02561704,-0.05691816,0.00313748,-0.01650501,0.01094952,0.03633237,0.00239417,-0.29785231,-0.01716909,-0.06570998,0.008053,0.03767126,0.02008492,0.04189295,0.03396137,-0.04195246,-0.00129992,-0.07122305,0.06630572,0.03041504,-0.01025462,0.03641934,-0.03912577,0.02435249,-0.00174477,0.03341461,-0.05897916,-0.00592452,0.06252018,0.20395818,-0.02538121,0.08419091,0.018996,0.01513312,0.08698882,0.00595524,0.05160662,0.00322864,-0.03674714,0.00319064,-0.03512475,0.05306482,0.0523136,0.00846094,0.01337269,0.05329161,-0.01624604,-0.04704659,0.05188358,-0.05413495,0.01489951,0.06027198,-0.04849127,-0.00581231,-0.04178988,0.0012602,0.04926733,0.00396087,0.00196624,-0.0003594,-0.04057958,-0.00160855,0.03439499,0.01033305,-0.02331405,-0.05981329,-0.02258214,0.01683771,0.00709539,0.10113205,0.11567935,0.07020336],"last_embed":{"hash":"7ac0bca1ca0044e6665841a196db2be711db34ef6959dc80306f2787b7c0a037","tokens":451}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.02387465,-0.01774187,-0.0051487,0.01167502,-0.02342849,0.02736684,-0.00829207,-0.02178136,-0.03633146,-0.03197642,-0.0089354,-0.02721423,-0.00893224,-0.0035991,0.01931315,0.00715655,-0.03430708,0.00801884,0.04403409,-0.0190479,-0.07051209,0.02584892,0.03434594,0.01992939,0.07269038,0.02521723,-0.01129625,-0.00525269,0.05112345,0.04190385,0.02332396,0.01115966,0.01732937,-0.01413116,-0.06099276,0.04806668,0.01849237,-0.05439847,0.04287051,-0.0096394,-0.02346027,-0.01698111,-0.00345003,0.01268468,-0.12718672,0.04602112,0.00868259,-0.03600261,-0.03087201,-0.01841104,0.02182012,-0.04595214,0.01130321,-0.02651965,0.03550486,-0.0040386,-0.01235331,-0.07385942,-0.05482805,-0.03036465,-0.05196808,-0.0248159,0.0218451,-0.02561774,0.04338075,-0.01178285,-0.00366546,-0.05465806,-0.02926109,0.00422336,-0.00751506,-0.03977732,0.058592,-0.01364533,0.04500605,0.03836694,0.00527396,-0.00015666,-0.04140614,0.01303741,-0.06927532,0.02337464,0.02113743,0.1008404,0.00227921,-0.0514113,0.06671779,-0.12759466,-0.01929071,0.02089287,0.01574349,0.00826409,-0.07335,0.0017677,0.01709546,-0.01672192,-0.00813713,-0.01136693,-0.02375677,-0.02148745,0.02801957,-0.03500888,-0.09736604,-0.01967108,-0.03948681,-0.04286104,-0.02401474,0.07972254,-0.02720329,-0.00706411,0.00349774,-0.02283805,0.00145771,-0.04275709,-0.00880681,0.01179529,0.00656644,-0.01728348,-0.01463449,0.03642598,0.0215503,0.02864992,0.01320615,-0.02967245,-0.0051943,0.04051116,0.00083778,0.00633863,-0.0467818,-0.03414953,0.0272662,0.0412625,-0.0377961,0.0425114,0.03150803,0.02284228,0.0130301,-0.06766035,0.00604811,0.00164477,0.04607218,-0.02785128,-0.03222685,-0.02234711,0.00113657,0.04919077,-0.02160434,-0.01326379,-0.036576,0.05187545,0.00715857,-0.06517763,-0.05965261,-0.02353671,-0.01208617,0.06120906,0.05847292,0.01416595,0.05506849,-0.02140471,-0.00032972,-0.0011886,-0.04732952,-0.01235227,-0.00489886,0.00352779,-0.06815121,0.00376263,-0.01453876,-0.02321953,0.05508569,0.00145348,-0.00115956,-0.01535421,0.02974453,-0.02106309,0.02688727,-0.02447156,-0.02654894,0.03601591,0.03512511,0.01531386,0.06159459,0.02028707,0.02009706,0.05617182,-0.06597689,0.03942821,0.08853286,0.02245717,0.00913225,-0.00900382,0.0070658,0.01756941,0.01859645,0.05547918,-0.01909837,-0.04775646,0.0184464,0.04526382,-0.01304746,-0.01956941,-0.01022778,-0.05788402,0.02013257,-0.00670277,-0.05661004,-0.01195652,0.01027175,0.00429563,0.09170824,-0.00706531,0.04752788,0.01124978,0.03075868,-0.00812701,0.03386054,0.05528383,0.00159726,0.00758176,0.05228554,-0.02146307,-0.06115568,-0.01535901,0.01401108,-0.03899202,0.00428479,0.02429336,0.02456567,0.00396462,-0.02851669,0.10675874,0.04378995,-0.01626075,-0.05286609,0.01233515,0.03879911,0.02442345,0.03190155,0.00856898,-0.02506403,-0.01224216,-0.01196685,0.0084555,0.01144112,-0.02957873,-0.04811652,0.00734879,0.02230698,0.03583427,0.05717199,0.06811983,-0.01655128,0.05036398,0.02606623,-0.01000291,-0.04683753,0.02608767,0.02169679,-0.05283517,0.02908593,-0.05485357,-0.02649641,0.05999436,-0.00481412,-0.01694884,-0.05008052,0.0039129,0.0017397,-0.01095589,0.08053016,-0.01320877,0.05200609,0.00019733,-0.01064412,0.00204954,0.0317151,0.00671475,-0.02874164,-0.02939231,0.03112821,-0.02772358,-0.02116787,-0.00264552,-0.02415011,0.01732925,0.02407145,-0.03871196,0.02309435,0.00683802,0.01174009,0.01513079,0.0022082,0.02684797,-0.05261391,-0.04384619,-0.00022563,-0.02727716,-0.05764399,-0.03402216,0.00677414,0.0235547,-0.05699415,-0.05452055,0.07181878,0.03199684,0.00273106,-0.04462959,-0.02173639,-0.02395581,0.00530131,-0.02633322,0.02078306,-0.03042885,-0.02164666,0.04178853,0.03884712,0.01647038,0.00169168,-0.04111499,-0.01622094,0.04945539,-0.08763634,0.00619589,-0.01334781,-0.07109578,-0.03218269,0.02916889,0.03068414,0.04232729,-0.02967013,0.01750731,-0.0093142,-0.03307537,0.02552538,0.07240966,-0.01350406,0.00245995,0.01156148,-0.03445922,-0.01587298,-0.03616723,-0.02013662,0.00778843,-0.00239673,0.00521208,0.00452145,0.03240723,-0.02806383,-0.00955659,0.00285414,-0.01027474,-0.06834542,0.01781135,0.00145868,0.03888228,-0.03176105,-0.00693659,-0.01773256,0.01114151,-0.08062782,-0.05645864,-0.00397913,0.01565684,-0.0265282,0.02474901,0.04092922,0.07257199,-0.07291523,-0.01210647,-0.04263033,-0.05421866,-0.00405912,0.03414615,-0.00336139,-0.00743593,-0.01343378,-0.00495344,0.04115116,-0.01767547,-0.03990693,-0.0575623,-0.03271718,-0.03382216,-0.00813043,-0.02566658,0.00214958,-0.03118843,0.07860559,-0.06783054,0.04360785,0.01736796,-0.03819652,-0.02479336,0.06957237,-0.0194323,0.02527875,-0.00094481,0.02577008,0.00633431,0.05890646,-0.01359503,-0.00372235,-0.0306596,-0.040078,0.02612831,-0.0401229,-0.01377529,-0.03277199,-0.02892212,-0.01419981,0.01489408,-0.00239612,0.03231939,-0.04242168,0.03501245,0.03362486,-0.03368778,-0.00458693,-0.00641315,0.02072341,-0.02515821,0.06728869,0.016123,-0.02248595,0.07537127,-0.06462984,-0.00315591,0.07597961,0.01538718,-0.02255408,-0.07376385,-0.03586059,-0.00391089,-0.04580247,-0.01889042,0.04854934,0.02459827,-0.01211639,0.03101483,-0.03450876,-0.02385403,-0.01863937,0.02493111,-0.00274276,0.04874998,-0.01613306,-0.00557471,-0.00329815,0.06243268,0.028559,0.00898619,-0.00183311,-0.00243666,0.02876925,0.0171554,0.00188181,0.06974446,0.04338727,0.02517728,-0.03833881,0.07186507,0.03998646,0.0030902,-0.02715169,0.06420176,0.09558062,0.01018504,-0.10153852,-0.00658961,-0.01425346,-0.01591855,0.02553828,0.00304396,-0.00991209,0.01651062,0.01355573,-0.03709362,-0.07804601,0.07508671,-0.05203435,0.02884229,-0.04176078,-0.01525536,-0.013493,-0.01531302,-0.00649213,0.04403786,-0.00204408,-0.06163645,-0.02953304,0.02291092,-0.01929866,0.01381395,-0.04725885,0.0042229,0.01705558,0.02762987,0.03440573,0.00135102,0.01216789,0.03941881,0.0117667,0.01782748,-0.00189365,-0.03222042,-0.03540899,-0.02804696,0.05634625,-0.0282362,0.01828628,-0.01779442,0.01604554,0.08986609,-0.02368797,-0.0539635,0.03486736,0.01728675,-0.00000624,0.02693597,0.01560053,0.05870606,-0.06300239,0.00560106,0.02130894,-0.03704054,-0.01374223,0.03720146,0.03175696,0.00906734,-0.01772802,0.01521385,0.01777868,-0.05836755,0.04212455,-0.01456269,0.03720763,-0.03569937,-0.02977817,-0.00913663,0.02126286,0.02828727,-0.02463443,0.00546471,-0.00178835,0.03497789,0.01611303,-0.03599885,0.02608693,0.03262944,0.00306828,0.00108673,-0.01952673,-0.03057486,-0.02810652,0.0080912,0.02287453,0.02064438,-0.07324106,0.05975901,0.01130965,0.04844653,0.02780838,0.00817819,0.00374883,0.02001448,-0.03289454,0.00204018,0.11029352,0.04166713,-0.11928633,0.02892491,0.09300988,-0.00242877,-0.02994576,-0.0070985,-0.0167385,-0.02300116,0.03060362,-0.05700777,0.04590403,-0.00583241,0.07460954,-0.02793432,-0.04339726,0.01523115,-0.02397768,0.03609473,0.01452521,-0.01027701,0.0611635,0.02200118,-0.0474693,0.03595898,0.03034325,-0.00467741,-0.06611633,0.05920349,0.01039953,0.00703543,0.01533861,0.0078017,-0.02004682,-0.04689895,0.01018106,-0.03120618,0.03400793,0.0013132,0.00777022,0.00168168,-0.02166484,0.08822783,-0.04149789,-0.05406284,-0.00987284,-0.01650299,-0.00449174,-0.01274417,-0.0373578,0.00035555,-0.03220235,-0.0199735,-0.03275499,0.02823655,-0.01060254,0.04606692,-0.01623716,0.04802377,0.00424011,0.00549513,0.02714271,-0.05806225,0.01209127,0.0733528,0.05302603,-0.00902615,-0.04627322,-0.01150875,0.06776075,-0.00272867,0.04346669,-0.01330781,-0.02290579,0.00962001,0.03156835,-0.07044183,-0.09257977,-0.02036825,0.02849916,0.02224496,0.06805884,-0.03201038,-0.00578899,-0.05893897,-0.00403091,0.02115047,0.04354809,-0.03947946,-0.00122545,0.02612998,-0.0167076,-0.02013833,0.00585447,0.04263392,-0.01742766,-0.01009047,0.08817108,0.0120849,-0.07948866,-0.03660363,0.00921135,-0.01559237,-0.01905509,0.01107141,-0.04504992,-0.00638109,0.03507021,0.0105613,-0.01396059,-0.01357373,-0.08894182,-0.00993363,0.01543974,0.03858984,0.05739297,0.01987656,-0.04954275,0.0080884,0.00560577,-0.02987252,-0.04755697,0.00498735,-0.00543155,-0.02202077,-0.02036376,-0.02427275,0.01178594,0.01212737,-0.03005979,-0.0067498,-0.02465679,-0.03033216,-0.03191805,0.05292553,-0.02325291,0.0279587,-0.01752003,0.02145924,-0.05210242,-0.0128491,0.00497892,0.02559518,-0.00560972,-0.03650656,0.01036567,-0.02267513,-0.02267813,0.04708615,-0.02006975,-0.00770368,0.03386739,0.02124427,-0.00965925,0.03638768,-0.01825528,-0.01292216,-0.05813618,-0.00949087,-0.05267116,0.0582934,0.01174714,-0.09021962,-0.01237236,0.01404087,0.05870846,0.02356862,-0.00877248,-0.00553693,0.03733423,-0.03331539,-0.00202441,-0.01953677,0.00178428,-0.05814611,-0.05190923,0.02049638,0.00671053,0.03867114,-0.00495995,0.0307616,-0.06369592,-0.01908691,-0.00771856,0.05758043,-0.00409689,-0.01909468,-0.01919805,-0.07381369,8.5e-7,0.05739516,-0.00070874,0.01850906,-0.02977524,-0.02231977,0.00039848,-0.05317115,0.02415805,0.01017719],"last_embed":{"tokens":962,"hash":"2gcyks"}}},"last_read":{"hash":"2gcyks","at":1751422355275},"class_name":"SmartSource","outlinks":[{"title":"docker","target":"docker","line":13},{"title":"FRP","target":"FRP","line":13},{"title":"linux","target":"linux","line":14},{"title":"windows系统","target":"windows系统","line":15},{"title":"docker","target":"docker","line":16},{"title":"#服务端配置","target":"#服务端配置","line":20},{"title":"docker","target":"docker","line":25},{"title":"#客户端配置","target":"#客户端配置","line":30},{"title":"proxies","target":"proxies","line":77},{"title":"proxies","target":"proxies","line":85},{"title":"proxies","target":"proxies","line":92},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":106},{"title":"TCP协议","target":"TCP协议","line":107},{"title":"SSH","target":"SSH","line":108},{"title":"TCP协议","target":"TCP协议","line":108}],"blocks":{"#简介(问题记录)":[1,31],"#简介(问题记录)#{1}":[2,17],"#---frontmatter---":[5,17],"#简介(问题记录)#{2}":[18,21],"#简介(问题记录)#{3}":[22,22],"#简介(问题记录)#客户端搭建(FRPC)":[23,31],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建":[25,31],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{1}":[26,28],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{2}":[29,29],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{3}":[30,30],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{4}":[31,31],"#基础设置":[32,111],"#基础设置#服务端配置":[34,66],"#基础设置#服务端配置#{1}":[35,60],"#基础设置#服务端配置#{2}":[61,64],"#基础设置#服务端配置#{3}":[65,66],"#基础设置#客户端配置":[67,111],"#基础设置#客户端配置#{1}":[68,102],"#基础设置#客户端配置#{2}":[103,104],"#基础设置#客户端配置#{3}":[105,107],"#基础设置#客户端配置#{4}":[108,108],"#基础设置#客户端配置#{5}":[109,111]},"last_import":{"mtime":1731396397438,"size":3454,"at":1748488128975,"hash":"2gcyks"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md","last_embed":{"hash":"2gcyks","at":1751422355275}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07929021,-0.03700351,-0.01763397,-0.06914685,0.01152737,-0.05479959,0.01391609,0.03113858,0.05566428,0.00153898,0.01163863,-0.08269727,0.02041512,0.04255587,0.017593,0.03247172,-0.02915833,0.01023977,-0.02495477,0.00177116,0.10242143,-0.03475511,-0.03563402,-0.09978926,0.01115375,0.06413344,0.00419217,-0.00013241,0.01457949,-0.13858023,-0.00855023,0.0084645,0.00052258,0.01048118,0.01876605,-0.01558877,0.04216361,0.03229793,-0.04135947,0.00666721,-0.0036829,0.01170042,0.00344527,-0.04642385,0.00586375,-0.06764926,-0.04225248,0.01857041,0.00433294,-0.03340795,-0.01146795,-0.006985,-0.01469065,-0.03175963,-0.0407037,0.00084807,-0.01676475,0.00290029,0.03147569,-0.03568516,0.04756219,0.04195487,-0.20501678,0.07147259,0.05314801,-0.0425799,-0.00325933,-0.01571633,0.04910146,0.04269898,-0.07441614,0.02149371,-0.02391473,0.04589688,0.03534457,-0.01543364,0.00044645,-0.01549506,0.00393999,-0.05777912,-0.04774563,0.04043423,-0.01451774,0.00292216,-0.01811971,0.00153107,-0.01148685,-0.0123232,0.01214653,-0.01643829,0.01154092,-0.06075924,0.03818123,-0.00089697,-0.03102384,0.06207739,0.01845738,0.06155102,-0.16017623,0.10807065,-0.04282118,0.01224082,-0.02220714,-0.10864542,0.00371879,-0.03207554,-0.01953395,-0.03117851,-0.02872087,0.00260874,-0.07105076,-0.03823517,0.03923894,-0.00706434,0.02209663,0.05367505,-0.00610574,0.00536032,-0.04932579,0.02476403,-0.00082481,-0.05892887,0.06037534,0.00026858,0.03158682,-0.01249697,0.04103922,0.06882264,0.00928956,0.04524826,0.03110814,-0.02662114,0.00198561,-0.02055985,0.01507227,0.04301597,-0.04575343,0.00626853,-0.00193834,-0.00839037,0.01596787,-0.07937761,-0.02574155,-0.09654715,-0.0862212,0.04124855,-0.01391472,0.00513546,0.05339679,-0.09142583,-0.00255658,0.07757588,-0.0028664,0.01684931,-0.03530696,0.02497676,0.09055391,0.15814812,-0.07779066,-0.0230109,-0.02445246,-0.00902601,-0.07797455,0.13904983,0.01382988,-0.064077,-0.05426782,-0.02192302,0.01500733,-0.03921766,0.00560029,-0.01867255,0.01317728,0.01655932,0.07529286,-0.00594889,0.00402374,-0.00936437,0.03944543,0.02053061,0.02812364,-0.02868954,-0.07724938,0.01210107,0.0319692,-0.08371778,-0.03420388,-0.01747219,0.01053347,-0.02430399,-0.11287194,0.01607961,-0.04731916,-0.00554281,-0.05124813,-0.0706064,0.0345341,0.01527065,0.05229434,-0.04919657,0.16020223,0.06972326,-0.03516176,0.04141881,-0.03712475,-0.00728827,-0.00323178,-0.01049031,-0.00347086,0.02532163,-0.03662247,0.02808114,0.02207544,0.03486514,-0.02328025,0.04507111,0.00815007,0.01296355,0.0430974,0.04206905,0.03618236,0.00745091,-0.05075483,-0.21228337,-0.05533334,0.02467472,-0.01523911,0.00971494,-0.00707766,0.02352625,-0.03166594,0.0563727,0.04360751,0.07970102,0.03352943,-0.04086558,-0.00835353,-0.00066205,0.01898186,0.02293626,-0.03380651,-0.00144699,0.00993344,-0.038685,0.03405689,-0.00874463,-0.02775664,0.03858142,-0.03257642,0.13214967,-0.01470719,0.01888021,0.00837624,0.00629804,0.01281675,0.02677061,-0.1009322,0.03458261,0.06647693,-0.03420596,-0.01006083,0.0505238,0.04127457,0.01784483,0.0172874,-0.05560717,-0.08131547,-0.00729841,-0.04727654,-0.01683649,-0.02649986,-0.00923213,0.03000956,0.03935039,0.01681854,0.00452161,0.03437784,-0.03280521,-0.04888816,-0.0146636,-0.0327023,-0.00872577,0.0328136,0.05099814,-0.02408247,0.0015448,0.03546387,0.04857564,-0.01392246,-0.00279745,0.01739742,0.03099507,-0.01863674,-0.03326868,0.14572343,-0.00286219,0.00751548,0.07699315,-0.0188954,-0.01909377,-0.02503565,0.0240539,0.05200235,0.03297345,0.03790887,0.03051225,-0.01586211,-0.01709692,0.01919982,0.01417325,0.01422808,0.09157757,-0.04526474,-0.05329419,-0.02350669,-0.01749961,0.01175252,0.05243453,0.0123817,-0.32187307,0.00968286,-0.06044411,0.02250539,0.05124184,0.03219406,0.08106632,0.04726925,-0.09228589,0.0115061,-0.03297629,0.02299261,-0.01432283,-0.03754902,0.02101588,-0.01458949,-0.01206917,-0.04533136,0.04277411,-0.03596589,-0.0121436,0.09228571,0.21085027,-0.02508997,0.06549108,0.01577497,0.044917,0.06119848,0.10749222,-0.0324212,0.03678311,-0.03375757,0.07768265,-0.01397323,0.04036992,0.03298329,-0.02374995,-0.03263972,0.04019717,-0.03102646,-0.06200271,0.04500561,-0.00734527,0.01512114,0.05605677,-0.0158134,-0.02635787,-0.04038893,0.03464103,0.05591025,0.02391257,-0.00753823,-0.02628903,-0.02021264,-0.00615156,0.03767916,0.03856227,-0.00458273,-0.08198331,-0.01687531,-0.0313073,0.02551219,0.10360146,0.12227786,0.03113892],"last_embed":{"hash":"11fc3de0176a3268cd3b4fb9ca6df791c8e31229ca9fe1ab52a6a29e47443d1e","tokens":501}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.02235596,-0.01019539,-0.00349078,-0.00006747,-0.0011615,0.00672762,0.0210748,-0.02811288,-0.01128354,-0.06389862,-0.02084828,0.02309953,-0.01678535,0.01122835,-0.00766536,0.01837718,-0.06058566,0.01693993,-0.00827726,-0.00471824,-0.06080745,-0.00337286,0.01669018,0.0086057,0.05701558,0.00318701,-0.04484085,-0.02609095,0.03394762,0.06833247,-0.00065714,-0.01556166,0.01727913,-0.01844504,-0.08434879,0.02945355,0.01159375,-0.00371786,0.01424998,0.00240232,-0.04923804,-0.02235622,-0.01443058,-0.00174701,-0.11338293,0.07262604,-0.0348428,-0.06819664,-0.0004353,-0.02303481,0.00323487,0.01927562,0.00849226,-0.04881734,0.01235237,0.00870602,-0.00593251,-0.0634653,-0.03838674,-0.07111236,-0.01682924,-0.01731976,-0.02136583,-0.05628079,0.05511291,-0.04570588,0.01128064,-0.04997079,-0.04267973,0.02247854,-0.04663995,-0.04047916,0.05117124,-0.01501531,0.01535278,0.03660023,-0.00205557,-0.0228306,-0.02185868,0.04540609,-0.04090044,0.03070982,0.03578499,0.09261773,-0.02418512,-0.04794115,0.03618422,-0.0825239,-0.02865202,0.00649384,-0.00069927,0.00363659,-0.0565448,-0.01641295,0.04340829,-0.03081955,0.02463654,0.00426336,0.00580987,-0.0364664,0.00758912,-0.02398074,-0.06361958,0.01977643,-0.02277345,-0.04527397,-0.05972151,0.04328253,-0.04122624,-0.0312898,0.03820927,-0.03714729,-0.02043977,-0.05862305,-0.01230416,0.01952614,-0.00178629,-0.03596056,-0.00398769,0.01247,0.0105952,0.00408512,0.00394743,-0.02256601,-0.03973,0.04154741,0.00584869,0.04081205,-0.04117171,-0.03127554,0.00216594,0.0266576,-0.02329198,0.03333428,0.02901974,0.01097884,0.0008413,-0.05373687,0.01440954,-0.01232388,0.03393789,-0.04259112,-0.0267135,-0.01462927,0.06977198,0.01454795,-0.02768,-0.00610339,-0.03280937,0.04390597,0.03795509,-0.0297956,-0.06299595,-0.02961197,-0.05942977,0.02761353,0.03664228,0.05564718,0.01748902,0.00983314,0.01676495,0.00818187,-0.0604845,-0.02132187,-0.01782658,0.04454302,-0.05696448,-0.00365365,-0.03483432,-0.00926506,0.05888094,0.03298199,-0.01352898,0.00905091,0.0463403,-0.02982791,0.04494872,-0.01500351,-0.00191098,0.04441787,0.06185962,-0.03586617,0.05539964,0.04007084,-0.02325721,0.08161531,-0.0424643,0.03403674,0.06713057,0.03251925,0.02985344,0.00799364,-0.00083495,-0.02694787,0.01160947,0.04199041,-0.02402294,-0.02118056,-0.01349311,0.04406476,-0.03929833,0.00826201,-0.01805778,-0.07332077,0.00982636,0.01188421,-0.03899142,-0.02835755,0.03153257,0.02937427,0.05917604,0.01561678,0.09487079,0.00427396,0.04088984,-0.03143167,0.01317589,0.0463886,-0.00765518,-0.0023137,0.04879524,-0.01465302,-0.05406978,0.00700368,0.02320522,-0.06571457,-0.01043596,0.00349799,0.01463168,-0.00066969,-0.04996715,0.09364779,0.02882668,0.01667075,-0.06037143,0.0279135,0.04357082,0.03270722,0.03385363,0.02488624,0.00704083,-0.04232201,-0.0166338,-0.01747503,0.00603877,-0.00785484,-0.05248312,0.01553263,0.01727305,0.05281971,0.07199477,0.05674584,-0.02176205,0.08537857,0.01362346,-0.03931016,-0.04195687,0.04829595,-0.03425701,-0.02418663,0.02676945,-0.03139477,-0.0070601,0.07903811,-0.01907096,-0.0198496,-0.03267751,0.01855785,0.03839903,-0.0033595,0.09802425,-0.01636036,0.05441273,-0.00558301,-0.02293421,0.01876244,0.04971935,-0.01769559,-0.05750461,0.01808187,0.0435524,-0.00150846,-0.00173102,-0.00191092,0.00281257,0.05737612,-0.0014427,-0.01753726,0.03664787,-0.0066894,0.01094946,-0.03140639,-0.01946249,0.01835794,-0.03987776,-0.0311438,-0.02305998,-0.00329803,-0.01439812,-0.06235798,-0.02148228,0.03129843,-0.07661119,-0.01523584,0.06435315,0.04285126,0.00241137,-0.05272595,0.00216674,-0.02386698,0.0030807,-0.0241899,-0.00310386,0.0115553,-0.04866173,0.01750378,0.03439195,0.02419729,0.00543572,-0.02547851,-0.03589376,0.05673799,-0.07920775,0.01600983,-0.01558597,-0.09048073,-0.01158957,0.02739497,0.01396817,0.04430542,-0.04709728,0.03031849,-0.0371227,-0.03432058,0.0059512,0.06228801,0.02533997,-0.04331068,-0.00492151,-0.04295931,0.02922324,-0.01933396,0.01980655,-0.01204163,0.02908773,0.0062333,0.00144224,0.020229,-0.02060966,0.00455646,0.03219856,-0.00336071,-0.07146646,0.0369938,0.00916558,0.00270147,-0.01685538,-0.02860682,0.00900895,-0.01397914,-0.07988975,-0.02677399,-0.04156625,0.0115881,0.00296896,0.02007691,0.02186833,0.06082706,-0.08278932,0.02517711,-0.03756985,-0.04617357,-0.0228057,-0.00748479,0.04410262,-0.00956506,-0.04646381,0.01412141,-0.00048119,-0.00872779,-0.04178892,-0.06909953,-0.04071354,-0.01166651,0.02075544,-0.00135464,-0.04214948,0.01163075,0.09535221,-0.02369617,0.05138381,0.00948083,-0.07260883,-0.04026954,0.06090169,0.01016431,0.0187504,0.02290669,0.02576433,0.01370091,0.03971165,0.01723112,-0.0113873,-0.0301017,-0.05208575,0.01805669,-0.05235684,-0.00424207,0.00541356,-0.03150282,-0.00866926,-0.02611522,-0.02906443,0.03809828,-0.01881867,0.01426844,0.03285722,-0.02823512,-0.02612458,-0.00273409,0.00904129,-0.01877572,0.05890793,0.00758903,-0.00302961,0.06194048,-0.03406987,-0.00386369,0.07376679,0.01444837,-0.02486265,-0.0754229,-0.05234083,0.01968825,-0.01062448,-0.03439848,-0.00158028,0.03362297,-0.02776187,-0.01250635,-0.01094196,-0.0104372,-0.0249766,0.01100549,-0.05162623,0.04902731,0.00589341,-0.01223535,0.01849835,0.06535788,0.02125372,-0.01507939,-0.01647854,-0.03109339,0.0382301,-0.01352412,-0.00674804,0.04632794,0.06842843,0.01866667,-0.03765498,0.05544664,0.02214047,0.03821575,-0.03225343,0.0135626,0.09235494,-0.00573084,-0.0923834,-0.00663342,0.0084758,-0.02939421,0.00399108,-0.01486297,-0.01774727,-0.000374,-0.00463534,-0.04027987,-0.08008683,0.05846555,-0.01858296,0.04231497,-0.01237399,-0.01723825,-0.00356303,-0.01245805,0.00807124,0.05024012,-0.03075868,-0.0730634,-0.01181989,0.0114697,0.00558235,-0.02607348,-0.05931703,0.00831819,0.01092556,0.03087788,0.03368582,0.0434183,-0.00660614,0.06223911,-0.00541816,0.04637585,-0.01149123,-0.0337951,-0.01277432,-0.00810047,0.04710025,-0.00956846,-0.00211069,-0.02800275,0.0498032,0.10331898,-0.02561251,-0.02576625,0.01036228,0.03042242,-0.01080492,-0.018414,0.02826985,0.0401004,-0.04499409,0.00976847,0.04910167,-0.05510211,-0.0200892,0.03647113,0.06768636,-0.00015045,0.02524787,-0.00876208,0.0335299,-0.02619099,0.02763197,0.00984638,-0.00899702,-0.0268584,-0.02365194,0.00979843,0.00344467,0.05229653,-0.04537393,0.05191189,0.01988248,0.03540752,0.0250735,-0.02905753,0.0236174,0.029733,0.00824295,0.04379742,0.01820639,-0.02584331,-0.01345815,0.0297147,-0.0117326,-0.00501334,-0.01699296,0.04471655,0.00236691,0.00791468,0.09073771,0.00096587,-0.00749395,0.0383266,-0.01271588,-0.01432083,0.06999103,0.01340163,-0.11343152,0.0117201,0.08280842,0.02116784,-0.02082211,0.01081093,-0.05144035,-0.01252873,0.03325956,-0.01332445,0.03826527,-0.01045497,0.07608695,-0.04697525,-0.01720689,-0.00025193,0.00159513,0.00649046,0.01135268,0.0111385,0.02817582,0.01059821,-0.06425129,0.04101482,0.04555103,0.0272117,-0.050259,0.03242302,-0.0033679,0.0038908,0.05054904,0.00617875,-0.01192222,-0.07609799,-0.01510758,-0.0207764,0.00364356,-0.03763863,-0.00668371,-0.02995253,-0.0346958,0.04958363,-0.06069187,-0.08658046,-0.00543221,-0.01120368,-0.0589048,-0.05683768,-0.01512047,-0.01317173,-0.04240249,-0.02749611,0.01042206,0.01710033,0.02887906,0.01225025,-0.01412603,0.04175239,-0.00373799,-0.03046951,0.01885217,-0.05075825,0.01666468,0.06103788,0.04303899,-0.02445176,-0.02842812,0.00175313,0.03177414,0.0020853,0.047091,-0.04574213,-0.01780827,0.0237091,0.01408286,-0.04594141,-0.08830077,-0.02956494,0.01902614,0.02239129,0.03384244,0.01836648,-0.00860165,-0.04757642,0.00129157,0.05313982,0.04542379,-0.04675001,-0.0090985,0.01837606,-0.02191606,-0.01132116,0.03460386,0.01907194,0.01537264,0.00548107,0.11482757,0.0157587,-0.041405,-0.0247476,0.06566143,-0.01039023,-0.00681176,0.00235551,-0.03033141,0.02739377,0.04053297,-0.00382389,-0.02959331,-0.0252495,-0.05898544,-0.03689033,0.04620698,0.02731323,0.05638444,0.05066977,-0.01628526,0.00732725,0.02269523,-0.04110675,-0.0234298,0.02030158,-0.01415617,-0.02860367,0.0155785,-0.00676753,-0.0164006,0.04365714,-0.04522866,0.0067027,0.00387434,-0.02981612,-0.0160509,0.02250401,-0.01854077,0.01523579,-0.04173128,0.05982374,-0.01782319,-0.01695283,-0.03742549,0.00504467,0.02140162,-0.0673013,0.00365387,-0.03226567,-0.00450681,0.04082679,0.00688163,0.00705368,0.02075388,0.0288824,-0.00055678,0.02901388,-0.06746197,-0.03026275,-0.06374896,0.0080303,-0.02625686,0.06018303,0.00423012,-0.06955637,0.00134155,0.05771122,0.0356975,0.01604755,-0.01137525,-0.01243528,0.03120265,-0.00674639,-0.00286242,0.00661031,0.00357995,-0.04302536,-0.03188801,-0.03529428,0.02597145,0.01845094,-0.01376258,0.06844843,-0.0883409,-0.00891553,-0.01982616,0.02450945,0.0100748,-0.02369426,-0.00191255,-0.06783456,8.5e-7,0.05444399,0.02000143,-0.00411138,-0.03562917,0.00409074,-0.00787952,-0.05691756,0.03101927,0.00055598],"last_embed":{"hash":"1kws79z","tokens":646}}},"text":null,"length":0,"last_read":{"hash":"1kws79z","at":1748397843798},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置","lines":[32,111],"size":1549,"outlinks":[{"title":"proxies","target":"proxies","line":46},{"title":"proxies","target":"proxies","line":54},{"title":"proxies","target":"proxies","line":61},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":75},{"title":"TCP协议","target":"TCP协议","line":76},{"title":"SSH","target":"SSH","line":77},{"title":"TCP协议","target":"TCP协议","line":77}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)","lines":[1,31],"size":690,"outlinks":[{"title":"docker","target":"docker","line":13},{"title":"FRP","target":"FRP","line":13},{"title":"linux","target":"linux","line":14},{"title":"windows系统","target":"windows系统","line":15},{"title":"docker","target":"docker","line":16},{"title":"#服务端配置","target":"#服务端配置","line":20},{"title":"docker","target":"docker","line":25},{"title":"#客户端配置","target":"#客户端配置","line":30}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)#{1}","lines":[2,17],"size":354,"outlinks":[{"title":"docker","target":"docker","line":12},{"title":"FRP","target":"FRP","line":12},{"title":"linux","target":"linux","line":13},{"title":"windows系统","target":"windows系统","line":14},{"title":"docker","target":"docker","line":15}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#---frontmatter---","lines":[5,17],"size":309,"outlinks":[{"title":"docker","target":"docker","line":9},{"title":"FRP","target":"FRP","line":9},{"title":"linux","target":"linux","line":10},{"title":"windows系统","target":"windows系统","line":11},{"title":"docker","target":"docker","line":12}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)#{2}","lines":[18,21],"size":94,"outlinks":[{"title":"#服务端配置","target":"#服务端配置","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)#{3}","lines":[22,22],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)","lines":[23,31],"size":225,"outlinks":[{"title":"docker","target":"docker","line":3},{"title":"#客户端配置","target":"#客户端配置","line":8}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建","lines":[25,31],"size":209,"outlinks":[{"title":"docker","target":"docker","line":1},{"title":"#客户端配置","target":"#客户端配置","line":6}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{1}","lines":[26,28],"size":129,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{2}","lines":[29,29],"size":33,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{3}","lines":[30,30],"size":22,"outlinks":[{"title":"#客户端配置","target":"#客户端配置","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{4}","lines":[31,31],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#服务端配置": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#服务端配置","lines":[34,66],"size":693,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#服务端配置#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#服务端配置#{1}","lines":[35,60],"size":579,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#服务端配置#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#服务端配置#{2}","lines":[61,64],"size":99,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#服务端配置#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#服务端配置#{3}","lines":[65,66],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#客户端配置": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#客户端配置","lines":[67,111],"size":846,"outlinks":[{"title":"proxies","target":"proxies","line":11},{"title":"proxies","target":"proxies","line":19},{"title":"proxies","target":"proxies","line":26},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":40},{"title":"TCP协议","target":"TCP协议","line":41},{"title":"SSH","target":"SSH","line":42},{"title":"TCP协议","target":"TCP协议","line":42}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#客户端配置#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#客户端配置#{1}","lines":[68,102],"size":623,"outlinks":[{"title":"proxies","target":"proxies","line":10},{"title":"proxies","target":"proxies","line":18},{"title":"proxies","target":"proxies","line":25}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#客户端配置#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#客户端配置#{2}","lines":[103,104],"size":53,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#客户端配置#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#客户端配置#{3}","lines":[105,107],"size":105,"outlinks":[{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":2},{"title":"TCP协议","target":"TCP协议","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#客户端配置#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#客户端配置#{4}","lines":[108,108],"size":47,"outlinks":[{"title":"SSH","target":"SSH","line":1},{"title":"TCP协议","target":"TCP协议","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#客户端配置#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md#基础设置#客户端配置#{5}","lines":[109,111],"size":5,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0862896,-0.05875936,0.02705097,-0.0194959,0.01845303,-0.00534725,0.00596357,0.02610843,0.0540526,-0.01708973,0.02673163,-0.0611427,0.03886777,0.00444548,0.01380941,0.05314081,0.01831845,0.03846177,0.02236341,-0.05161541,0.0618456,-0.01646807,0.00755886,-0.10545804,-0.00123357,0.06142835,-0.00035323,-0.01190198,0.02087358,-0.15614164,0.03101119,0.04294452,-0.04264385,0.01196328,0.02148816,-0.02939422,0.0059276,0.01774995,-0.03457314,0.04215825,0.03201216,-0.00084013,-0.02371047,-0.01391272,-0.01137237,-0.04882977,-0.01780758,-0.00614633,0.00935858,-0.07009139,-0.01787374,-0.04605598,-0.01336714,-0.06837823,-0.01329588,0.0521067,0.04271741,0.00554508,0.06246879,-0.10493853,0.03734134,0.02735208,-0.22715035,0.07005568,0.07135711,-0.00860706,0.00261165,0.00040874,0.0169422,0.05388031,-0.08257305,0.05254437,-0.02119097,0.05273438,0.06636799,-0.02627828,-0.01251822,-0.02169417,-0.0049252,0.0020416,-0.01317066,0.00849941,-0.01922498,0.00144471,-0.03418099,0.01068219,-0.00000984,0.00233698,-0.0001658,-0.00975036,-0.02341319,-0.05361446,0.04403425,0.04110833,-0.02292982,0.00713573,0.01592848,0.05181104,-0.12741132,0.08637661,-0.04650898,0.02960466,-0.0168019,-0.05727071,0.04605373,-0.05896758,-0.03021462,-0.02509686,-0.01287543,-0.00405114,-0.03769906,-0.02709853,0.05307391,-0.0514693,0.04146672,0.02480081,-0.00216448,0.00562806,-0.05536664,-0.00118096,-0.02884067,0.00257588,0.03841712,-0.01271763,-0.02683817,-0.06025382,0.06239465,0.07649897,-0.00679008,0.01443827,0.03728394,-0.03453914,0.01624791,0.01096589,0.03918225,0.04128223,0.01467713,-0.05328878,-0.01696248,-0.05821094,0.01954918,-0.0828558,0.01285863,-0.09527571,-0.0584297,0.06072983,-0.06266388,0.04420226,0.04767064,-0.06264956,-0.02160763,0.02087115,-0.00400823,-0.05731723,0.00988147,0.01446577,0.10581618,0.15858722,-0.04367861,-0.0285296,-0.06502442,0.00409121,-0.09405357,0.13084573,0.02685213,-0.05476625,-0.04252555,0.00027149,-0.02071943,-0.02709553,-0.01515777,-0.04631064,0.02095316,-0.02759256,0.07617763,-0.00670676,-0.03339579,-0.04055701,0.02000398,0.02170761,0.02955176,-0.02342081,-0.03995081,-0.01691783,-0.01367986,-0.0718834,-0.06211292,-0.04410197,-0.00859753,-0.05947896,-0.11968724,0.0048841,0.05058772,0.01463625,-0.02708683,-0.06135388,0.02475387,0.01754727,0.09035686,-0.03961388,0.10675166,0.02330087,0.01508561,0.02035749,-0.06803694,-0.00385227,-0.02017314,0.00442902,0.03464985,-0.02680613,-0.0342884,0.02375884,-0.01215585,0.00943625,-0.01023699,0.05325721,-0.01230456,0.0100225,0.00118362,0.05607568,0.08500579,0.0151691,-0.06581437,-0.20708567,-0.04484741,0.00370624,-0.04244376,0.0256812,-0.01805873,0.0276972,0.00894846,0.00932198,0.02653795,0.04712673,-0.01694481,-0.04060037,0.01571704,-0.00538607,-0.01772507,0.0682827,-0.01865274,-0.01226498,0.00258406,-0.03158837,0.0741692,-0.05910929,-0.05196114,0.04978632,0.02479215,0.14090347,-0.00942031,0.05223808,0.02302555,0.03929453,0.07491145,0.01587285,-0.11653672,0.00613262,0.0814138,0.04161761,-0.0307142,0.01152892,0.05153428,-0.01502321,0.04809168,-0.03166154,-0.08013038,-0.03344918,-0.05489213,0.0005923,-0.03311672,-0.03594155,-0.00771691,-0.02324867,-0.02777659,-0.00502742,0.00858965,0.00024777,-0.04166688,-0.04852812,-0.0223332,-0.00386619,0.03057037,0.05461552,0.04137726,-0.0039857,0.03727289,0.05484701,-0.01539836,-0.0201231,0.03890949,0.02910476,-0.01555962,-0.02324613,0.12104082,0.03307879,0.00062701,0.06545887,-0.03190377,-0.0188074,-0.07678773,0.00878914,0.02188995,0.07879443,0.04203482,0.06635109,0.01751194,0.03306688,0.00523809,0.01111663,0.05101211,0.12381763,-0.02561704,-0.05691816,0.00313748,-0.01650501,0.01094952,0.03633237,0.00239417,-0.29785231,-0.01716909,-0.06570998,0.008053,0.03767126,0.02008492,0.04189295,0.03396137,-0.04195246,-0.00129992,-0.07122305,0.06630572,0.03041504,-0.01025462,0.03641934,-0.03912577,0.02435249,-0.00174477,0.03341461,-0.05897916,-0.00592452,0.06252018,0.20395818,-0.02538121,0.08419091,0.018996,0.01513312,0.08698882,0.00595524,0.05160662,0.00322864,-0.03674714,0.00319064,-0.03512475,0.05306482,0.0523136,0.00846094,0.01337269,0.05329161,-0.01624604,-0.04704659,0.05188358,-0.05413495,0.01489951,0.06027198,-0.04849127,-0.00581231,-0.04178988,0.0012602,0.04926733,0.00396087,0.00196624,-0.0003594,-0.04057958,-0.00160855,0.03439499,0.01033305,-0.02331405,-0.05981329,-0.02258214,0.01683771,0.00709539,0.10113205,0.11567935,0.07020336],"last_embed":{"hash":"7ac0bca1ca0044e6665841a196db2be711db34ef6959dc80306f2787b7c0a037","tokens":451}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.02387465,-0.01774187,-0.0051487,0.01167502,-0.02342849,0.02736684,-0.00829207,-0.02178136,-0.03633146,-0.03197642,-0.0089354,-0.02721423,-0.00893224,-0.0035991,0.01931315,0.00715655,-0.03430708,0.00801884,0.04403409,-0.0190479,-0.07051209,0.02584892,0.03434594,0.01992939,0.07269038,0.02521723,-0.01129625,-0.00525269,0.05112345,0.04190385,0.02332396,0.01115966,0.01732937,-0.01413116,-0.06099276,0.04806668,0.01849237,-0.05439847,0.04287051,-0.0096394,-0.02346027,-0.01698111,-0.00345003,0.01268468,-0.12718672,0.04602112,0.00868259,-0.03600261,-0.03087201,-0.01841104,0.02182012,-0.04595214,0.01130321,-0.02651965,0.03550486,-0.0040386,-0.01235331,-0.07385942,-0.05482805,-0.03036465,-0.05196808,-0.0248159,0.0218451,-0.02561774,0.04338075,-0.01178285,-0.00366546,-0.05465806,-0.02926109,0.00422336,-0.00751506,-0.03977732,0.058592,-0.01364533,0.04500605,0.03836694,0.00527396,-0.00015666,-0.04140614,0.01303741,-0.06927532,0.02337464,0.02113743,0.1008404,0.00227921,-0.0514113,0.06671779,-0.12759466,-0.01929071,0.02089287,0.01574349,0.00826409,-0.07335,0.0017677,0.01709546,-0.01672192,-0.00813713,-0.01136693,-0.02375677,-0.02148745,0.02801957,-0.03500888,-0.09736604,-0.01967108,-0.03948681,-0.04286104,-0.02401474,0.07972254,-0.02720329,-0.00706411,0.00349774,-0.02283805,0.00145771,-0.04275709,-0.00880681,0.01179529,0.00656644,-0.01728348,-0.01463449,0.03642598,0.0215503,0.02864992,0.01320615,-0.02967245,-0.0051943,0.04051116,0.00083778,0.00633863,-0.0467818,-0.03414953,0.0272662,0.0412625,-0.0377961,0.0425114,0.03150803,0.02284228,0.0130301,-0.06766035,0.00604811,0.00164477,0.04607218,-0.02785128,-0.03222685,-0.02234711,0.00113657,0.04919077,-0.02160434,-0.01326379,-0.036576,0.05187545,0.00715857,-0.06517763,-0.05965261,-0.02353671,-0.01208617,0.06120906,0.05847292,0.01416595,0.05506849,-0.02140471,-0.00032972,-0.0011886,-0.04732952,-0.01235227,-0.00489886,0.00352779,-0.06815121,0.00376263,-0.01453876,-0.02321953,0.05508569,0.00145348,-0.00115956,-0.01535421,0.02974453,-0.02106309,0.02688727,-0.02447156,-0.02654894,0.03601591,0.03512511,0.01531386,0.06159459,0.02028707,0.02009706,0.05617182,-0.06597689,0.03942821,0.08853286,0.02245717,0.00913225,-0.00900382,0.0070658,0.01756941,0.01859645,0.05547918,-0.01909837,-0.04775646,0.0184464,0.04526382,-0.01304746,-0.01956941,-0.01022778,-0.05788402,0.02013257,-0.00670277,-0.05661004,-0.01195652,0.01027175,0.00429563,0.09170824,-0.00706531,0.04752788,0.01124978,0.03075868,-0.00812701,0.03386054,0.05528383,0.00159726,0.00758176,0.05228554,-0.02146307,-0.06115568,-0.01535901,0.01401108,-0.03899202,0.00428479,0.02429336,0.02456567,0.00396462,-0.02851669,0.10675874,0.04378995,-0.01626075,-0.05286609,0.01233515,0.03879911,0.02442345,0.03190155,0.00856898,-0.02506403,-0.01224216,-0.01196685,0.0084555,0.01144112,-0.02957873,-0.04811652,0.00734879,0.02230698,0.03583427,0.05717199,0.06811983,-0.01655128,0.05036398,0.02606623,-0.01000291,-0.04683753,0.02608767,0.02169679,-0.05283517,0.02908593,-0.05485357,-0.02649641,0.05999436,-0.00481412,-0.01694884,-0.05008052,0.0039129,0.0017397,-0.01095589,0.08053016,-0.01320877,0.05200609,0.00019733,-0.01064412,0.00204954,0.0317151,0.00671475,-0.02874164,-0.02939231,0.03112821,-0.02772358,-0.02116787,-0.00264552,-0.02415011,0.01732925,0.02407145,-0.03871196,0.02309435,0.00683802,0.01174009,0.01513079,0.0022082,0.02684797,-0.05261391,-0.04384619,-0.00022563,-0.02727716,-0.05764399,-0.03402216,0.00677414,0.0235547,-0.05699415,-0.05452055,0.07181878,0.03199684,0.00273106,-0.04462959,-0.02173639,-0.02395581,0.00530131,-0.02633322,0.02078306,-0.03042885,-0.02164666,0.04178853,0.03884712,0.01647038,0.00169168,-0.04111499,-0.01622094,0.04945539,-0.08763634,0.00619589,-0.01334781,-0.07109578,-0.03218269,0.02916889,0.03068414,0.04232729,-0.02967013,0.01750731,-0.0093142,-0.03307537,0.02552538,0.07240966,-0.01350406,0.00245995,0.01156148,-0.03445922,-0.01587298,-0.03616723,-0.02013662,0.00778843,-0.00239673,0.00521208,0.00452145,0.03240723,-0.02806383,-0.00955659,0.00285414,-0.01027474,-0.06834542,0.01781135,0.00145868,0.03888228,-0.03176105,-0.00693659,-0.01773256,0.01114151,-0.08062782,-0.05645864,-0.00397913,0.01565684,-0.0265282,0.02474901,0.04092922,0.07257199,-0.07291523,-0.01210647,-0.04263033,-0.05421866,-0.00405912,0.03414615,-0.00336139,-0.00743593,-0.01343378,-0.00495344,0.04115116,-0.01767547,-0.03990693,-0.0575623,-0.03271718,-0.03382216,-0.00813043,-0.02566658,0.00214958,-0.03118843,0.07860559,-0.06783054,0.04360785,0.01736796,-0.03819652,-0.02479336,0.06957237,-0.0194323,0.02527875,-0.00094481,0.02577008,0.00633431,0.05890646,-0.01359503,-0.00372235,-0.0306596,-0.040078,0.02612831,-0.0401229,-0.01377529,-0.03277199,-0.02892212,-0.01419981,0.01489408,-0.00239612,0.03231939,-0.04242168,0.03501245,0.03362486,-0.03368778,-0.00458693,-0.00641315,0.02072341,-0.02515821,0.06728869,0.016123,-0.02248595,0.07537127,-0.06462984,-0.00315591,0.07597961,0.01538718,-0.02255408,-0.07376385,-0.03586059,-0.00391089,-0.04580247,-0.01889042,0.04854934,0.02459827,-0.01211639,0.03101483,-0.03450876,-0.02385403,-0.01863937,0.02493111,-0.00274276,0.04874998,-0.01613306,-0.00557471,-0.00329815,0.06243268,0.028559,0.00898619,-0.00183311,-0.00243666,0.02876925,0.0171554,0.00188181,0.06974446,0.04338727,0.02517728,-0.03833881,0.07186507,0.03998646,0.0030902,-0.02715169,0.06420176,0.09558062,0.01018504,-0.10153852,-0.00658961,-0.01425346,-0.01591855,0.02553828,0.00304396,-0.00991209,0.01651062,0.01355573,-0.03709362,-0.07804601,0.07508671,-0.05203435,0.02884229,-0.04176078,-0.01525536,-0.013493,-0.01531302,-0.00649213,0.04403786,-0.00204408,-0.06163645,-0.02953304,0.02291092,-0.01929866,0.01381395,-0.04725885,0.0042229,0.01705558,0.02762987,0.03440573,0.00135102,0.01216789,0.03941881,0.0117667,0.01782748,-0.00189365,-0.03222042,-0.03540899,-0.02804696,0.05634625,-0.0282362,0.01828628,-0.01779442,0.01604554,0.08986609,-0.02368797,-0.0539635,0.03486736,0.01728675,-0.00000624,0.02693597,0.01560053,0.05870606,-0.06300239,0.00560106,0.02130894,-0.03704054,-0.01374223,0.03720146,0.03175696,0.00906734,-0.01772802,0.01521385,0.01777868,-0.05836755,0.04212455,-0.01456269,0.03720763,-0.03569937,-0.02977817,-0.00913663,0.02126286,0.02828727,-0.02463443,0.00546471,-0.00178835,0.03497789,0.01611303,-0.03599885,0.02608693,0.03262944,0.00306828,0.00108673,-0.01952673,-0.03057486,-0.02810652,0.0080912,0.02287453,0.02064438,-0.07324106,0.05975901,0.01130965,0.04844653,0.02780838,0.00817819,0.00374883,0.02001448,-0.03289454,0.00204018,0.11029352,0.04166713,-0.11928633,0.02892491,0.09300988,-0.00242877,-0.02994576,-0.0070985,-0.0167385,-0.02300116,0.03060362,-0.05700777,0.04590403,-0.00583241,0.07460954,-0.02793432,-0.04339726,0.01523115,-0.02397768,0.03609473,0.01452521,-0.01027701,0.0611635,0.02200118,-0.0474693,0.03595898,0.03034325,-0.00467741,-0.06611633,0.05920349,0.01039953,0.00703543,0.01533861,0.0078017,-0.02004682,-0.04689895,0.01018106,-0.03120618,0.03400793,0.0013132,0.00777022,0.00168168,-0.02166484,0.08822783,-0.04149789,-0.05406284,-0.00987284,-0.01650299,-0.00449174,-0.01274417,-0.0373578,0.00035555,-0.03220235,-0.0199735,-0.03275499,0.02823655,-0.01060254,0.04606692,-0.01623716,0.04802377,0.00424011,0.00549513,0.02714271,-0.05806225,0.01209127,0.0733528,0.05302603,-0.00902615,-0.04627322,-0.01150875,0.06776075,-0.00272867,0.04346669,-0.01330781,-0.02290579,0.00962001,0.03156835,-0.07044183,-0.09257977,-0.02036825,0.02849916,0.02224496,0.06805884,-0.03201038,-0.00578899,-0.05893897,-0.00403091,0.02115047,0.04354809,-0.03947946,-0.00122545,0.02612998,-0.0167076,-0.02013833,0.00585447,0.04263392,-0.01742766,-0.01009047,0.08817108,0.0120849,-0.07948866,-0.03660363,0.00921135,-0.01559237,-0.01905509,0.01107141,-0.04504992,-0.00638109,0.03507021,0.0105613,-0.01396059,-0.01357373,-0.08894182,-0.00993363,0.01543974,0.03858984,0.05739297,0.01987656,-0.04954275,0.0080884,0.00560577,-0.02987252,-0.04755697,0.00498735,-0.00543155,-0.02202077,-0.02036376,-0.02427275,0.01178594,0.01212737,-0.03005979,-0.0067498,-0.02465679,-0.03033216,-0.03191805,0.05292553,-0.02325291,0.0279587,-0.01752003,0.02145924,-0.05210242,-0.0128491,0.00497892,0.02559518,-0.00560972,-0.03650656,0.01036567,-0.02267513,-0.02267813,0.04708615,-0.02006975,-0.00770368,0.03386739,0.02124427,-0.00965925,0.03638768,-0.01825528,-0.01292216,-0.05813618,-0.00949087,-0.05267116,0.0582934,0.01174714,-0.09021962,-0.01237236,0.01404087,0.05870846,0.02356862,-0.00877248,-0.00553693,0.03733423,-0.03331539,-0.00202441,-0.01953677,0.00178428,-0.05814611,-0.05190923,0.02049638,0.00671053,0.03867114,-0.00495995,0.0307616,-0.06369592,-0.01908691,-0.00771856,0.05758043,-0.00409689,-0.01909468,-0.01919805,-0.07381369,8.5e-7,0.05739516,-0.00070874,0.01850906,-0.02977524,-0.02231977,0.00039848,-0.05317115,0.02415805,0.01017719],"last_embed":{"tokens":962,"hash":"2gcyks"}}},"last_read":{"hash":"2gcyks","at":1751441877167},"class_name":"SmartSource","outlinks":[{"title":"docker","target":"docker","line":13},{"title":"FRP","target":"FRP","line":13},{"title":"linux","target":"linux","line":14},{"title":"windows系统","target":"windows系统","line":15},{"title":"docker","target":"docker","line":16},{"title":"#服务端配置","target":"#服务端配置","line":20},{"title":"docker","target":"docker","line":25},{"title":"#客户端配置","target":"#客户端配置","line":30},{"title":"proxies","target":"proxies","line":77},{"title":"proxies","target":"proxies","line":85},{"title":"proxies","target":"proxies","line":92},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":106},{"title":"TCP协议","target":"TCP协议","line":107},{"title":"SSH","target":"SSH","line":108},{"title":"TCP协议","target":"TCP协议","line":108}],"blocks":{"#简介(问题记录)":[1,31],"#简介(问题记录)#{1}":[2,17],"#---frontmatter---":[5,17],"#简介(问题记录)#{2}":[18,21],"#简介(问题记录)#{3}":[22,22],"#简介(问题记录)#客户端搭建(FRPC)":[23,31],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建":[25,31],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{1}":[26,28],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{2}":[29,29],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{3}":[30,30],"#简介(问题记录)#客户端搭建(FRPC)#基于[[docker]]搭建#{4}":[31,31],"#基础设置":[32,111],"#基础设置#服务端配置":[34,66],"#基础设置#服务端配置#{1}":[35,60],"#基础设置#服务端配置#{2}":[61,64],"#基础设置#服务端配置#{3}":[65,66],"#基础设置#客户端配置":[67,111],"#基础设置#客户端配置#{1}":[68,102],"#基础设置#客户端配置#{2}":[103,104],"#基础设置#客户端配置#{3}":[105,107],"#基础设置#客户端配置#{4}":[108,108],"#基础设置#客户端配置#{5}":[109,111]},"last_import":{"mtime":1731396397438,"size":3454,"at":1748488128975,"hash":"2gcyks"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口转发/工具/FRP.md","last_embed":{"hash":"2gcyks","at":1751441877167}},