"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08525978,0.00806061,-0.01225721,-0.06242554,0.0182901,0.00267047,-0.01461847,-0.00348667,0.05255949,0.00885268,0.00212726,-0.06539,0.03660679,0.0980781,0.08651213,0.00575583,-0.00084037,-0.00952304,0.0018394,0.0369339,0.08280709,-0.0404039,-0.02487605,-0.06384072,-0.03018927,0.00209277,0.01233192,0.04077397,-0.03341515,-0.12514442,0.04713699,-0.02330243,-0.03903649,-0.00909988,-0.01736918,-0.06209103,0.05165775,0.01896015,-0.01403772,0.0118051,0.00755191,0.06027508,-0.00282187,-0.01409932,-0.03872139,-0.0892021,-0.03926839,-0.00382429,0.01186495,-0.02928067,-0.0220617,-0.04458049,-0.04341904,0.00757825,-0.0285989,-0.00530207,0.04055259,0.04545258,0.00950068,0.00137815,0.09092765,-0.02497565,-0.18780056,-0.00377592,0.02325508,-0.04888869,-0.01301162,-0.02834019,0.0601249,0.04837689,-0.09988585,0.06025599,-0.04985833,0.04623505,0.01308557,0.00008069,-0.00337343,0.00182114,-0.03108006,-0.06948966,-0.02647313,0.04432376,-0.00118387,0.03769343,-0.00545917,0.04427633,-0.01572345,0.01792881,0.0253156,0.05090852,-0.01237682,-0.0775076,0.03754419,0.02930242,0.00980014,0.03719856,0.01433213,0.10535364,-0.09492498,0.13650858,-0.04174354,-0.01068828,-0.011145,-0.0771498,-0.01855043,-0.03571646,0.00987333,-0.01777519,0.02779197,-0.0124657,-0.06112238,-0.01726409,0.00175297,-0.01258298,0.00026472,0.03250642,-0.01050314,0.02723337,-0.01688621,-0.04220483,-0.0704794,-0.00897356,0.07536254,-0.04672914,-0.01604696,-0.03111213,0.05019839,0.05890481,0.05247983,0.05783734,0.04625706,-0.00693549,-0.03735124,-0.05770452,-0.00781431,-0.03048128,-0.02097755,0.00433876,0.01293436,-0.03766312,-0.02519126,-0.10221173,-0.00955365,-0.08258548,-0.06283417,0.09383815,-0.03608275,-0.00490113,0.00181776,-0.0357466,0.02212053,0.09155915,-0.00133158,-0.00644729,-0.00770544,0.03694391,0.05113028,0.14522295,-0.02369999,-0.05099537,0.01381594,0.02444669,-0.06535814,0.15088084,0.05991868,-0.09844469,-0.06215612,0.01393045,0.00079547,0.00781176,0.00579721,-0.01997109,0.01260836,-0.02621266,0.03584101,-0.01429345,-0.00474292,-0.02225967,-0.00221631,0.02396041,0.0460519,-0.03395053,-0.05521735,0.05308391,-0.0246465,-0.12463149,-0.02421265,-0.0256219,0.00790994,-0.07752223,-0.12120847,0.01871621,-0.05906837,0.02252447,0.00331235,-0.0653341,0.03807434,0.01404145,0.02945172,-0.03739494,0.11907153,0.05146375,-0.02887053,-0.00929986,0.00024556,0.00277367,0.02841972,0.01735302,0.02557047,0.04935586,-0.01213602,-0.00089146,0.00210101,0.05178924,-0.01888981,-0.01809974,0.01226247,0.00868302,0.02519152,0.08251605,-0.04233804,-0.0158725,-0.07491446,-0.20792848,-0.05044106,-0.01429852,-0.02566565,0.04774646,-0.02417038,0.06081887,0.04315027,0.05533049,0.11219,0.1368072,0.03455448,-0.05116277,-0.00396619,0.02690167,-0.02057796,0.02255954,0.00525792,-0.01455356,-0.01524516,-0.020744,0.03480971,0.01504102,0.00643172,0.05889851,-0.01632068,0.13611273,0.02347141,-0.01052893,-0.01414846,0.0150135,0.03392402,0.01985616,-0.10703279,0.006306,0.02484725,-0.02312086,-0.02698204,-0.00429373,0.01845473,-0.00354889,0.03379665,-0.01577575,-0.07194185,-0.00426089,-0.02088277,-0.02709615,-0.03252302,-0.01162587,0.09627383,-0.02790746,-0.05063969,-0.02177014,0.03752881,0.01479462,-0.03887951,-0.0367992,-0.05438443,-0.00004201,0.04362784,0.04144514,0.00841852,0.02863369,-0.01569644,0.00925919,-0.0109915,-0.05936975,-0.00011354,-0.00492996,-0.02690873,-0.07384604,0.17449647,-0.01042456,0.00444826,0.04388061,0.02299576,0.02466081,-0.0494552,0.01130212,0.0076965,0.05826976,-0.00377198,0.02226218,-0.04441018,-0.00599624,0.03672016,-0.01731025,-0.00631853,0.07894936,-0.03036842,-0.02929053,-0.04643982,-0.09549802,0.00755871,0.06747486,-0.00455506,-0.28272972,0.05018274,-0.03497419,0.02454902,-0.01505153,0.00403917,0.04711204,0.0314411,-0.06326518,0.04428567,-0.03244711,0.07131267,0.01421606,-0.0713245,-0.00217131,-0.01652422,0.01556693,0.00520352,0.0644242,-0.03418056,0.03568074,0.0329428,0.20675778,0.04000929,0.0554422,0.00198579,0.00313841,-0.0107019,0.06239041,0.00771138,0.01431961,-0.06752192,0.06912325,-0.0290051,0.06183515,0.03219765,-0.04094448,-0.01251551,0.02420203,-0.00998234,-0.05283969,0.04404725,-0.04313838,0.08809473,0.07607096,0.03261692,-0.00274489,-0.02455495,0.02382268,0.01603395,0.00418004,0.03507909,-0.02117582,-0.02456565,0.01056075,0.03561786,0.00084578,-0.03493423,-0.03904374,0.04278769,0.00646589,-0.03961619,0.08927547,0.08739471,0.01742725],"last_embed":{"hash":"5281f7cad25de228a432abad5292a4ad5b43bbbf2712026019237b9d00a20848","tokens":487}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.03979107,-0.01461233,0.00791229,-0.04765332,0.00391682,0.02533163,0.03911385,-0.00972745,-0.04093221,-0.03034964,0.00513431,-0.01997904,-0.03730615,-0.04524745,0.04371992,0.05014747,-0.01352094,-0.02805778,0.08887137,0.01018745,-0.01379654,0.0368425,-0.00328235,0.01818953,-0.009697,-0.04636801,-0.01491084,-0.05937427,-0.0060242,0.04785111,0.07742021,0.01213673,0.02277542,-0.0112316,-0.06227886,-0.00324208,0.01501867,-0.00517115,-0.00672177,-0.04297245,-0.01766233,0.01157507,-0.0653811,0.04477818,-0.06514655,0.02184735,-0.00650458,-0.04155248,0.02327665,0.00296972,0.02618157,-0.0151077,0.01109839,0.02630167,-0.00238894,0.05611429,0.0260618,-0.06348795,-0.01336727,-0.03060623,0.01046813,0.03504707,0.06642643,0.03282272,0.02437971,0.01960467,0.00292599,0.01857508,0.02187081,-0.02388216,-0.07227582,-0.01690145,0.0478405,0.03173424,0.01548354,-0.00794154,0.00651786,-0.00192664,-0.04283241,0.02260745,-0.05307222,0.05367373,-0.02327264,0.11093701,0.00440175,-0.03646854,0.00607267,-0.07401875,0.01265072,0.00628743,0.00553066,0.03515732,-0.02892373,-0.0130165,0.04387816,-0.01733477,-0.01328593,0.02329188,0.00572319,-0.04530211,0.05812199,0.03015735,-0.03323164,0.00176558,-0.07283897,-0.07774065,-0.0464046,-0.01941178,-0.01342801,-0.01154611,-0.04298092,-0.00067913,-0.05428074,-0.03577168,-0.02057309,0.03525095,-0.03076178,-0.03394688,0.00513374,0.03622294,0.00778203,0.04157953,-0.02722008,-0.0070785,-0.11426109,0.01031501,-0.00684514,-0.04343976,0.00205555,-0.00175487,-0.04089535,0.08082231,-0.02380501,-0.02405359,0.03175262,-0.02065173,0.06957687,-0.06956892,0.02445478,-0.01200738,0.00600633,-0.02152293,-0.03972424,-0.00677613,-0.0175122,0.05375585,-0.07450306,-0.03273426,-0.0017567,0.03367186,0.02779282,-0.01255213,-0.05243901,-0.03800456,-0.01425101,0.00415714,0.05414092,0.06805339,0.02190269,-0.01035898,-0.00992672,0.01321331,-0.00737727,-0.01393566,0.00505368,0.00135091,-0.05910098,0.03363093,0.02126699,-0.04402297,0.03283217,0.00440643,0.0018632,0.00408564,0.02896824,-0.0105182,0.06927873,0.00844241,-0.05637243,0.00227315,0.03624111,0.03921805,0.00940128,0.03001996,-0.00149892,0.03648756,-0.02515886,0.04914628,0.07308536,0.00688048,0.04399516,0.03102059,0.0352242,0.01278168,0.04288423,0.03801668,-0.02294192,-0.07434282,0.01098658,0.02850038,-0.01215985,-0.03337089,-0.05858673,-0.09585375,0.05326136,-0.02590304,-0.02735861,-0.01549017,-0.06582692,0.0599791,0.00369242,-0.00629448,0.03316479,0.02206175,-0.03611935,0.01867274,-0.02748586,0.06393321,0.03669067,-0.01658286,-0.003863,-0.01610845,-0.07647201,0.00405232,-0.01603108,-0.06032194,-0.00549451,-0.03111157,-0.00343436,0.03314297,-0.00430543,0.03577645,-0.00644296,0.00597486,-0.0424966,-0.01524009,0.04994713,0.04706991,-0.00531928,0.01356946,0.02461914,0.0059361,0.00445774,-0.01738908,-0.02138831,-0.03235282,-0.02507527,0.05393168,0.00878491,0.00871805,0.05401577,0.03705753,0.03705921,0.05202341,0.00336303,-0.06035627,-0.04307374,0.0098376,-0.00859251,-0.04945508,0.03601485,-0.04498402,-0.01912777,0.03891635,0.00509758,-0.00914829,0.01223142,0.00918928,-0.01281401,0.02654598,0.0299963,-0.04564223,0.05687572,0.03249663,0.04272104,-0.04264915,-0.00284748,-0.02908405,-0.01078017,-0.02260775,0.01061186,-0.05011302,0.03219029,0.01885383,-0.03767987,0.07377777,-0.00637549,-0.00829806,0.03543444,-0.00648673,-0.00717585,0.00817006,-0.0500109,0.05614178,-0.07307816,-0.0593679,0.0066836,0.0075595,0.01257527,-0.03246669,0.0344492,0.05398696,-0.00934546,-0.05100221,0.01208404,0.02510877,0.00379386,-0.00121787,0.00768056,-0.05067025,-0.00814903,-0.04979949,0.0100188,-0.02936392,0.00086609,0.09124859,0.03528321,-0.02002667,-0.00371444,-0.01761561,-0.0583319,0.01797336,-0.01811688,0.01969682,0.05694781,-0.11515436,0.0263522,0.00300679,0.00149622,0.01700385,-0.08032004,0.02826513,-0.02276159,-0.02443678,-0.00842221,-0.00757378,-0.0141023,-0.0638862,0.04818767,-0.05399641,0.04441082,-0.06131814,0.02246502,-0.01269223,0.01116815,0.04301818,-0.01707272,0.02671961,-0.03219767,-0.04188611,0.02631635,-0.01609477,-0.05068406,0.04407508,0.0190757,0.00247911,-0.06938034,-0.00449603,-0.04000404,0.00538941,-0.02463266,-0.03293852,-0.03242506,-0.03980799,-0.02141778,0.01176907,0.04058778,0.05102063,-0.05418144,-0.05964212,-0.01311882,-0.00147796,-0.05963797,-0.01601692,0.02540991,-0.00723651,-0.03289192,0.0413259,0.05257193,-0.01475058,-0.02203041,-0.07639163,0.0026334,-0.00806181,-0.05099676,-0.05421926,-0.02181034,-0.00530603,-0.03088989,-0.07850785,-0.00695845,0.03806638,-0.04208101,-0.02037746,0.02754971,0.02184721,0.05403084,-0.00284194,0.02139429,0.00094481,0.02620482,-0.01961073,-0.02387542,0.01113945,-0.02128721,0.02287722,-0.06351577,0.01961669,-0.02438608,-0.01043659,-0.02074388,0.02655949,-0.01202676,0.02340226,0.01457597,0.04642432,0.030398,-0.07609339,0.0052766,-0.01807042,0.03902896,-0.00892766,0.03865993,0.09211759,0.00532173,0.01192915,-0.08093012,-0.01073098,-0.02053729,-0.00314335,-0.07142912,-0.03607007,-0.09591552,-0.04202023,-0.02034058,-0.00969708,0.0348582,-0.01224965,0.02038217,-0.01076884,-0.0776191,-0.03068468,-0.01738734,-0.00358483,0.03963833,0.02417442,0.00530165,-0.0518779,0.00650867,0.05032601,-0.00414145,-0.06293106,-0.02101973,-0.01386549,0.0293257,0.00491974,0.0199224,-0.0426113,0.01182401,0.01502085,-0.05396437,-0.0177856,0.02928633,0.01761409,0.00837822,-0.00472152,0.01744596,0.04397572,0.00550097,-0.00696597,-0.01620337,-0.01504433,0.02402423,-0.03439789,0.05177812,0.03030087,0.00416129,-0.00295362,-0.04866624,-0.01453511,-0.00157735,0.03980543,-0.01713653,0.00248367,-0.04759794,0.02631559,-0.03122109,0.03024523,-0.02880656,-0.04362751,-0.03843217,0.08241326,0.02574925,0.01991192,-0.00549343,0.02468562,0.05825511,0.04634136,-0.0091085,0.06699201,0.00791378,0.02811211,-0.02165557,0.00267528,0.06640016,0.00566278,-0.03477693,0.02608969,0.04564228,-0.061818,-0.01738546,-0.00043351,0.03776049,0.01757971,0.0264032,-0.00546037,-0.04400424,-0.02731179,0.00020357,-0.02242473,-0.02022716,0.01765276,-0.06022902,0.00258056,0.01763387,0.00629961,0.01438256,0.07780663,0.01495816,0.01231252,0.00646598,0.01939701,0.03594249,-0.0504835,0.00434629,-0.01524416,0.03322358,-0.02471576,-0.01411445,0.04346877,0.06848561,-0.0093033,-0.0228247,0.02934047,-0.02044615,-0.03677269,0.06084031,-0.007021,0.05021099,0.00750437,0.00890191,-0.01457561,-0.0036505,-0.01391283,-0.02573465,0.02172603,0.00658105,-0.0246096,-0.01649156,0.11266628,0.03900559,0.04305831,0.02238,0.02314952,-0.01723524,-0.0145406,-0.02667111,-0.00928338,0.04438349,0.02507963,-0.09040571,0.01091788,0.03668655,-0.00622614,-0.0844312,0.02506186,0.01873926,0.0038635,0.04956161,0.01928809,0.02438638,-0.00256123,0.11798542,-0.00345212,-0.01644643,0.00080484,-0.01823929,0.04408057,0.00350927,-0.00829453,0.04949768,-0.02526408,-0.04015723,-0.01124321,-0.00712285,0.0220721,-0.03228636,0.0404133,0.01864256,-0.02817139,0.01139736,0.00350576,-0.03781504,0.02279719,0.00213276,-0.06899265,-0.00382056,-0.0725458,0.01202308,0.01009113,-0.00379688,0.07373502,-0.00187202,0.01101285,0.02023134,-0.06459234,-0.03922022,-0.01024213,-0.01503564,-0.01371641,-0.03829067,-0.03726713,-0.00650198,-0.00235287,0.0080931,0.0590343,0.010215,0.00334673,0.02229586,-0.05195491,-0.0472951,0.00869399,0.00852026,0.04570334,0.02217157,0.03545816,-0.04293617,-0.04541911,-0.0127719,-0.02499671,0.01385603,0.01170352,0.01616317,-0.00624313,0.01455147,-0.00154665,-0.07653799,-0.02236659,0.00767149,0.00201807,0.05050334,-0.02116796,0.01798532,0.00601872,0.00035909,0.03885892,0.00629491,0.00818536,0.01058602,0.0654082,-0.02456583,-0.02418988,0.02216298,0.05312772,-0.05965918,0.01883439,0.0525368,-0.00488639,-0.03786517,0.03246004,-0.03973984,-0.04272469,-0.00954456,0.02125813,-0.01306897,0.02397096,0.02527325,0.02501725,-0.00549193,-0.02988189,-0.08092791,-0.04556955,0.04842936,0.01641289,0.05949001,0.03116439,-0.01653366,0.01855981,-0.01170346,0.00122206,-0.09216993,-0.00302714,-0.01288322,-0.00472354,0.04033687,-0.03986805,0.01005578,0.00042111,-0.02382652,0.03924337,-0.03701217,-0.00789797,-0.04171325,0.05165618,-0.0268673,-0.03095458,0.04955327,0.04491153,0.02720829,0.00021135,0.00501972,-0.03812275,0.01121767,-0.04353633,0.03335707,-0.06064076,0.01849599,0.0448252,-0.07588733,-0.03190082,-0.00583749,0.02757474,0.04114337,0.07823505,-0.02959993,-0.03770797,-0.06648762,0.02047038,-0.01122515,0.02233966,-0.04143935,-0.05167983,0.02705685,0.07613575,0.04146487,-0.01030739,-0.01663135,0.00186851,0.07620938,-0.02221574,-0.01998677,-0.01398769,-0.01265493,-0.04621167,-0.04377434,0.03441764,-0.01986556,0.02875717,0.00758587,0.03377894,-0.05461366,-0.03384584,-0.00401379,0.08331498,0.0344036,-0.01751676,0.01148356,-0.03421536,0.00000107,0.00627971,0.02512019,0.05036318,-0.01791815,-0.02809982,-0.04231286,-0.06108574,0.03782282,-0.00156874],"last_embed":{"tokens":916,"hash":"11pqm3f"}}},"last_read":{"hash":"11pqm3f","at":1751422355693},"class_name":"SmartSource","outlinks":[{"title":"500","target":"Pasted image 20240709172620.png","line":21},{"title":"#可利用协议","target":"#可利用协议","line":32},{"title":"PHP","target":"PHP","line":75},{"title":"Trivial File Transfer Protocol","target":"TFTP","line":75},{"title":"TFTP","target":"TFTP","line":75},{"title":"轻量级目录访问协议","target":"LDAP协议","line":80},{"title":"SMTP协议","target":"SMTP协议","line":84}],"metadata":{"aliases":["Server-Side Request Forgery","服务器端请求伪造"],"英文":"Server-Side Request Forgery","工具界面":["命令行","GUI界面"]},"blocks":{"#---frontmatter---":[1,9],"#简介":[10,24],"#简介#{1}":[11,12],"#简介#{2}":[13,14],"#简介#{3}":[15,24],"#漏洞原理":[25,46],"#漏洞原理#{1}":[26,29],"#漏洞原理#{2}":[30,31],"#漏洞原理#{3}":[32,33],"#漏洞原理#{4}":[34,35],"#漏洞原理#{5}":[36,36],"#漏洞原理#file_get_contents() 与 readfile()函数的利用":[37,46],"#漏洞原理#file_get_contents() 与 readfile()函数的利用#{1}":[38,46],"#漏洞原理#file_get_contents() 与 readfile()函数的利用#{2}":[41,46],"#可利用协议":[47,84],"#可利用协议#**file://**":[49,55],"#可利用协议#**file://**#{1}":[50,55],"#可利用协议#**file://**#{2}":[52,55],"#可利用协议#**dict://**":[56,64],"#可利用协议#**dict://**#{1}":[57,57],"#可利用协议#**dict://**#{2}":[58,58],"#可利用协议#**dict://**#{3}":[59,64],"#可利用协议#**dict://**#{4}":[60,64],"#可利用协议#**SFTP://**":[65,72],"#可利用协议#**SFTP://**#{1}":[66,66],"#可利用协议#**SFTP://**#{2}":[67,72],"#可利用协议#**SFTP://**#{3}":[68,72],"#可利用协议#**TFTP://**":[73,77],"#可利用协议#**TFTP://**#{1}":[75,75],"#可利用协议#**TFTP://**#{2}":[76,77],"#可利用协议#**LDAP://**":[78,81],"#可利用协议#**LDAP://**#{1}":[80,81],"#可利用协议#**SMTP**":[82,84],"#可利用协议#**SMTP**#{1}":[84,84]},"last_import":{"mtime":1731306039781,"size":3556,"at":1748488128975,"hash":"11pqm3f"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md","last_embed":{"hash":"11pqm3f","at":1751422355693}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08926322,-0.04627451,0.00786577,-0.05991663,0.02661475,-0.02808381,-0.03188316,0.01606719,0.03364122,-0.01659941,0.02074956,-0.05331089,0.06819875,0.05668202,0.04491702,0.03999532,-0.01829148,-0.00977059,-0.0181023,0.03351202,0.09762804,-0.03219335,-0.00370895,-0.09902237,-0.04458544,0.01185983,0.0339026,0.00962162,-0.05146539,-0.13037215,0.0087438,-0.02149284,-0.0383251,0.03625897,-0.01139766,-0.04174746,0.0144121,0.01743572,-0.01253742,0.01869992,-0.03129303,0.04592124,0.02559304,-0.00610387,-0.04117121,-0.06964613,-0.05595567,-0.01680765,0.0124189,-0.02464097,-0.03718473,-0.00073638,-0.05770997,-0.00987275,-0.06164762,-0.02313681,0.05978788,0.00898554,0.0229207,-0.02749518,0.0580393,0.03964681,-0.19824316,0.04855051,0.04461425,-0.03702633,-0.04464639,-0.01165149,0.02797269,0.02197056,-0.11384802,0.03493316,-0.02021487,0.06204916,0.0596715,-0.04388399,0.01643605,-0.01078184,-0.04202678,-0.0338632,-0.01249633,0.07229656,-0.01590506,0.03102217,-0.01697751,0.0469113,-0.00910013,-0.00643095,-0.01112342,-0.00011443,-0.01073313,-0.04920448,-0.00676941,0.03481175,-0.01344607,0.00168693,0.0268634,0.08053598,-0.08735561,0.09158374,-0.06428503,0.00264354,0.00842524,-0.0681017,0.00178898,-0.02306534,0.01701196,0.01994475,-0.01408884,0.02453737,-0.04528625,-0.06198619,0.0147403,-0.02414137,0.03032265,0.05266739,-0.02364262,0.03278468,-0.04804344,-0.00108302,-0.02805936,-0.01394032,0.10033707,-0.03542181,-0.03527688,-0.06031946,0.03505491,0.06998064,0.05373577,0.06482194,0.02928475,-0.04671183,-0.0527615,-0.04514184,-0.03114266,0.01367699,-0.06775496,-0.01124081,-0.01105359,-0.02265933,-0.02497101,-0.08238763,0.00280162,-0.08927445,-0.07246196,0.08171641,-0.03425904,-0.02272148,0.05565687,-0.03040911,0.04951137,0.06997059,-0.02545476,-0.04068756,-0.04539177,0.02007822,0.08862264,0.10082714,-0.02736313,-0.02536846,-0.01225728,0.01056744,-0.09192421,0.16617988,0.00409331,-0.08821312,-0.04819177,0.00381359,0.03343056,-0.05790679,0.01666577,-0.0161096,0.00902215,-0.04555274,0.06697609,-0.00881295,-0.00972957,-0.05253765,-0.01596444,0.00534008,0.04076704,-0.00272737,-0.07952002,0.04589998,-0.00932098,-0.05911117,-0.01329421,-0.00676553,0.00143116,-0.01372174,-0.10125923,0.00432736,-0.05634448,-0.03711132,-0.05881998,-0.08849303,0.03709016,-0.01213182,0.0290235,-0.01595038,0.11040497,0.05863199,-0.04536713,0.00578484,-0.01787847,0.0048108,0.02425516,0.00705462,0.00510357,0.05383839,0.02442769,0.04119327,-0.0078204,0.02817266,-0.00745389,0.02944236,0.01867795,0.00542639,0.06256258,0.05235598,0.00864326,-0.04568589,-0.06341609,-0.19157113,-0.05088892,0.0159224,-0.0427643,0.07439834,-0.04416778,0.01583493,0.02261273,0.06096083,0.09071503,0.12923905,0.06021362,-0.05551787,-0.01267994,0.02476687,0.01436387,-0.00949275,0.03311156,-0.03610384,0.02486521,-0.01791454,0.01813645,0.03918444,-0.03281011,0.0337479,-0.07697944,0.13118382,0.03051705,0.03867676,0.00471823,0.03479812,0.01947165,0.01089108,-0.13630638,0.02893087,0.01113827,-0.03618575,-0.02084398,0.01071025,0.01219123,0.03796693,0.02818369,-0.02268923,-0.05456812,0.00138599,-0.02882488,-0.04112092,-0.02507473,-0.00967484,0.06843899,0.03186549,0.01068609,-0.00162115,-0.00103028,-0.0014678,-0.0145427,-0.02605178,-0.02184082,-0.01565689,0.04649554,0.01113286,0.01818714,0.03447847,0.0127394,0.0149866,-0.03488946,-0.04997912,0.02840891,-0.01279833,0.04627912,-0.00527222,0.14473896,-0.01543026,0.04996791,0.04073166,0.01617811,-0.00817516,-0.02889401,0.03194333,-0.01849458,0.05894777,0.06784294,0.04399271,-0.01316595,-0.03888816,0.0404588,0.04221388,-0.03353579,0.08498938,-0.0310315,-0.05213133,-0.07928614,-0.02789549,0.04448558,0.05415731,-0.01797224,-0.3238214,0.00753344,-0.0357054,-0.00607204,0.0406484,0.01746243,0.06744759,0.04143816,-0.05716676,0.00992729,0.00697076,0.02995471,-0.00358259,-0.05373833,0.01938196,-0.03500182,0.04042653,0.03949209,0.06362642,-0.03505896,-0.01369538,0.05158804,0.19575243,0.00423663,0.07019009,0.01855679,0.00059929,0.04919136,0.08675702,0.01731808,0.00742123,-0.02872944,0.00664662,-0.0529859,0.05381007,0.01827759,-0.00772074,-0.01341742,0.04488495,-0.03312939,-0.05557945,0.04604775,-0.05886346,0.01957985,0.08146394,0.02060032,0.01190214,-0.05254717,0.01495995,0.03109284,0.02575539,0.0430556,-0.00856243,-0.018816,0.02260508,0.06522366,0.05465107,-0.01480954,-0.0451231,-0.04091844,0.00323674,0.00066724,0.09819776,0.09018032,0.06365816],"last_embed":{"hash":"dbca659310a42bdc550a0069a235505f06c858d3266b074423d4151365cad4a9","tokens":461}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.05133772,-0.00547123,-0.00264445,-0.01969321,0.01356342,-0.00648484,0.03277369,-0.01128087,-0.03560196,-0.02569194,-0.00542356,-0.04086656,-0.05222519,-0.05115239,0.05067046,0.09147047,-0.00781728,-0.01565799,0.07080132,-0.02710113,-0.04356045,0.02006435,-0.01380914,0.04392356,0.01291086,-0.01820156,-0.03149932,-0.01988283,0.04818376,0.05858733,0.07386741,0.00845058,0.0040489,-0.00656295,-0.05466817,-0.01942418,0.02055566,0.01686056,0.02530564,-0.02413405,-0.02699495,0.01574334,-0.05562956,0.05022928,-0.0521361,0.00366805,0.0014195,-0.04511886,0.01817239,-0.00955928,-0.00818225,-0.00180865,0.04281909,-0.00472885,-0.00439171,0.06571443,0.00145323,-0.07385664,-0.00820061,-0.04145634,0.04058876,0.00000908,0.06419153,0.04679578,0.03220908,0.01087184,0.0323115,-0.00048633,0.03889718,-0.00434131,-0.06329746,-0.02383432,0.05918202,0.01253362,0.0413421,-0.0160474,0.02245753,-0.0023888,-0.00376806,0.02737189,-0.05668102,0.03184763,0.00274579,0.05832475,-0.00288419,-0.03125876,0.04012484,-0.05796608,0.03253219,0.0266858,0.00074879,0.04205035,-0.00857812,-0.0383499,0.02096805,-0.03718353,-0.00849467,0.00200443,0.01780039,-0.01658071,0.04422744,-0.01051713,-0.03354913,-0.02436527,-0.08622634,-0.06867287,-0.03767687,-0.00364624,-0.02766318,0.00000914,-0.02015842,-0.01142235,-0.03549506,-0.01569317,-0.01857661,0.03517419,0.0046192,-0.038259,0.03403116,0.05242332,0.00735493,0.03008683,-0.01075679,-0.02181824,-0.10836252,-0.00913894,-0.01524417,-0.03422507,0.00602452,-0.0086838,-0.01450272,0.07486173,0.00111505,-0.02616807,0.02804902,0.01099246,0.0461449,-0.05408026,0.03123397,-0.01801337,0.0043744,-0.02212369,-0.01777086,-0.02756425,-0.03503006,0.07476885,-0.06100837,-0.02185558,-0.0280754,0.05408718,0.03846816,-0.01456788,-0.07588188,-0.02535259,0.00329744,-0.01829046,0.02231819,0.02001436,0.02801767,-0.02166609,-0.01584473,0.00779676,0.01099178,-0.00146686,0.00712125,-0.00208468,-0.04681138,0.06045208,0.02121411,-0.01628232,0.02766108,0.02139094,0.0150286,0.01261788,0.00899119,-0.02028815,0.08610366,0.00715528,-0.05645949,-0.00562566,0.07446977,0.02860737,0.01635346,0.04617232,-0.00187997,0.03711173,-0.03294995,0.0461139,0.05349,0.00880214,0.04488996,0.01184862,0.02491262,0.03710134,0.0566512,0.05057916,-0.00097093,-0.1116012,0.01407259,0.03410143,-0.02131269,-0.00995406,-0.0611179,-0.08177557,0.05540535,-0.02493636,-0.00418794,-0.0133622,-0.04619119,0.0368102,-0.01505822,0.00111261,0.00753004,0.01831617,-0.00400974,0.03219071,-0.00092041,0.03733007,0.05084315,-0.03114354,0.00942476,-0.02941407,-0.02606611,0.01607628,0.01480745,-0.04021373,0.0080551,-0.0388974,-0.01443553,0.04225535,-0.04763127,0.07318541,0.00619319,-0.00566478,-0.05846645,-0.01881442,0.06082676,0.04336184,0.02679395,-0.00280815,0.00103736,0.00826485,0.00111779,-0.03936926,-0.02524782,-0.02229629,-0.04936509,0.03843897,0.00837504,0.03262437,0.05864083,0.04107114,0.01426992,0.04052466,0.00760843,-0.03785555,-0.04233211,-0.02680552,0.01453817,-0.05617793,0.03668772,-0.03263314,-0.00358528,0.06123884,0.00669377,-0.0420674,0.01803389,0.00346534,-0.0230062,0.02015333,0.02202853,-0.04258519,0.05472579,-0.01581139,0.04298417,-0.03782996,0.00489842,-0.01009176,-0.03863629,-0.0225724,0.01726608,-0.03582176,0.00459529,-0.03606944,-0.05056443,0.09178955,0.0193615,-0.01037869,0.03827506,0.00163754,-0.01352206,0.01779219,-0.02895302,0.06636272,-0.07951114,-0.04839127,-0.00503165,-0.00005459,0.00387824,-0.01450011,0.01469221,0.0545155,-0.00721598,-0.04780531,0.02264888,0.01030405,0.0029218,0.0019067,-0.00332447,-0.05663684,0.00796569,-0.05874894,0.00586613,-0.02285965,-0.00338465,0.09215654,0.03452838,-0.01242771,-0.00419194,-0.02152059,-0.07395104,0.02355276,-0.03901364,0.00997966,0.03353858,-0.10453822,0.0297287,0.02270965,0.01855013,0.01454056,-0.08991995,0.01599058,-0.01898305,-0.01759322,0.01398854,-0.01040894,-0.01774217,-0.04148473,0.03178946,-0.04791611,0.00705272,-0.03632887,0.03182242,-0.03175193,0.03489389,0.08074854,-0.02188009,0.00835402,-0.08219297,-0.07144298,0.0287376,0.00728426,-0.05764978,0.02655792,0.04632894,-0.01438507,-0.06973053,0.00226638,-0.05442384,-0.00101061,-0.0516639,-0.02195655,-0.04808291,-0.02634409,-0.03038374,-0.00336259,0.03045652,0.05096303,-0.03717936,-0.02709297,-0.0510096,0.02830907,-0.04652945,-0.0045501,0.00563247,-0.00579485,-0.04919922,0.02550009,0.06000632,-0.02000704,-0.05132874,-0.07134099,-0.01596246,-0.01604458,-0.028619,-0.06332205,-0.00416252,-0.02758792,-0.01538964,-0.0746418,0.00258384,0.02464853,-0.05613487,-0.00251125,0.05880927,0.0028134,0.02056911,-0.01448236,0.0133014,-0.02605717,0.02887286,-0.00633861,0.01766092,-0.00841684,-0.00228274,0.03600634,-0.08637334,0.06080183,-0.00175639,-0.04513077,-0.02091477,0.04292123,-0.00136907,0.02136102,0.01840283,0.0292743,0.00140671,-0.07103106,-0.01442444,-0.01329854,0.05014056,-0.01465066,0.03376225,0.06746361,-0.00732518,0.02443793,-0.0750877,-0.01262612,-0.01282798,0.01115491,-0.06142128,-0.02038291,-0.0671135,-0.01384354,-0.00392572,-0.02818039,0.02287694,0.00167106,0.01530914,-0.03354909,-0.06440729,-0.02029857,-0.03137825,-0.00224211,0.05249216,0.01256192,-0.00724192,-0.03431766,0.02996259,0.05958313,0.00338771,-0.051149,-0.02919872,-0.00504122,0.00235028,-0.00489018,0.03068229,-0.02215837,-0.00362476,0.00611374,-0.03532704,0.00999321,0.0225286,0.02487055,0.01393457,0.0275404,0.04125906,0.03415961,-0.01300848,0.01255723,-0.01838745,-0.02691056,0.03343579,-0.03134437,0.02880207,0.03977831,0.00106873,-0.00999147,-0.03544743,-0.01359074,-0.02946143,0.0248758,-0.01453431,0.00759554,-0.01481871,0.01715141,0.00295638,0.02973093,-0.00834263,-0.04487885,-0.01930562,0.06694618,0.05083236,0.02770637,-0.01936713,0.00117709,0.04914046,0.03917751,-0.01649559,0.04938805,0.00973892,0.01574733,-0.01244645,0.01680624,0.04209628,-0.00510446,-0.02639704,0.02511385,0.07064798,-0.02883178,-0.00396067,-0.00681971,0.03483412,0.05282228,-0.00832802,0.00115409,-0.04788421,0.01783897,0.02095902,-0.01830242,-0.01554468,0.0271504,-0.06865337,0.00052671,0.00014297,0.00910758,0.01847479,0.08205422,0.05113741,0.01151126,-0.01937138,0.02947429,0.00826414,-0.05073117,0.0148335,-0.02329337,0.03351626,-0.02813066,-0.02803972,0.00284491,0.05773521,0.01815338,-0.0404966,0.04807675,-0.00890121,-0.03438957,0.05783942,-0.01469243,0.04919827,0.01669405,0.01512793,0.01632264,-0.00046418,0.00062061,-0.04695954,0.00877338,0.00216996,-0.02771269,0.00520787,0.11499192,0.00071907,0.05287516,0.0270923,-0.005676,0.01288287,-0.00949013,-0.04935485,-0.02595256,0.05414004,0.00526626,-0.09772437,0.00675567,0.04152468,-0.03035375,-0.08277289,0.01031501,-0.01004291,-0.00373264,0.0425404,0.00901532,0.03234061,-0.00306944,0.10168394,-0.01677258,-0.02795563,0.01714573,-0.0050403,0.03080633,0.00243691,-0.03749854,0.02426946,-0.05293675,-0.02195972,-0.01902948,-0.02600225,-0.02335643,-0.04720349,0.03009618,0.02022122,-0.02509517,0.01446808,0.00988326,-0.01532115,0.02993437,-0.00028001,-0.07390988,-0.02326101,-0.07228761,-0.00189886,0.01840411,0.01080967,0.08686327,-0.00438163,-0.01972678,-0.00270005,-0.05178688,-0.0470267,0.02001083,-0.04829508,0.00426937,-0.03626957,-0.07132792,-0.00719943,-0.0288183,0.0182928,0.07188284,0.01981998,0.03246003,0.00608646,-0.05399659,-0.05542066,-0.02998295,-0.01421192,0.06312881,0.02026749,0.04482234,-0.03536142,-0.04112719,-0.00833312,-0.02188504,0.04637564,0.02293602,0.00797969,0.01177719,0.02182966,-0.04986843,-0.07978289,-0.02926913,0.01677866,0.0206148,0.01466671,-0.02715807,-0.01103657,-0.00994218,-0.0124561,0.06729414,-0.00933268,0.01960143,-0.017804,0.04137206,-0.03598819,-0.03596073,-0.0237872,0.04638901,-0.05092727,0.04105645,0.02455129,-0.00233641,-0.02160814,0.011768,-0.02447788,-0.02617717,-0.03217952,0.02788966,-0.04694495,-0.00193357,0.01804487,0.03435713,-0.04674644,-0.02734541,-0.08297181,-0.01191976,0.04121633,0.02319129,0.05916792,0.01621771,-0.00761181,-0.01191336,-0.02121168,0.01385617,-0.06539688,0.00992638,0.01216738,-0.0012253,0.01417255,-0.02957537,0.02040118,0.00192518,-0.02829928,0.02305682,-0.04053617,-0.00776235,-0.02466694,0.06856215,-0.03395085,-0.01626653,0.02821483,0.02947213,0.02385251,-0.02584952,0.00935392,-0.02983915,-0.00648076,-0.04933917,0.0241777,-0.05973571,-0.00370264,0.04698908,-0.09706622,-0.03182955,-0.01054747,0.0247971,0.04498911,0.05238574,-0.04123616,-0.02757101,-0.0355601,0.02479668,0.00719078,0.02941456,-0.04262383,-0.06877283,0.02382277,0.09122115,0.02445899,-0.03712814,0.02303308,-0.00687226,0.055107,0.00839744,0.00379921,0.00082076,-0.00529301,-0.02983087,-0.03641381,0.04065227,-0.00164362,0.02183184,0.04966915,0.02333103,-0.00020645,0.02044594,-0.00255314,0.09108172,0.03575702,0.00964516,0.02441175,-0.04738806,9.6e-7,-0.00046156,0.04055922,0.07858253,-0.04320951,-0.03237327,-0.05264479,-0.03578499,0.05713089,0.00025389],"last_embed":{"hash":"no19c1","tokens":470}}},"text":null,"length":0,"last_read":{"hash":"no19c1","at":1748397843361},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议","lines":[47,84],"size":1006,"outlinks":[{"title":"PHP","target":"PHP","line":29},{"title":"Trivial File Transfer Protocol","target":"TFTP","line":29},{"title":"TFTP","target":"TFTP","line":29},{"title":"轻量级目录访问协议","target":"LDAP协议","line":34},{"title":"SMTP协议","target":"SMTP协议","line":38}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#---frontmatter---","lines":[1,9],"size":117,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#简介","lines":[10,24],"size":308,"outlinks":[{"title":"500","target":"Pasted image 20240709172620.png","line":12}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#简介#{1}","lines":[11,12],"size":44,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#简介#{2}","lines":[13,14],"size":55,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#简介#{3}","lines":[15,24],"size":202,"outlinks":[{"title":"500","target":"Pasted image 20240709172620.png","line":7}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#漏洞原理": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#漏洞原理","lines":[25,46],"size":520,"outlinks":[{"title":"#可利用协议","target":"#可利用协议","line":8}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#漏洞原理#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#漏洞原理#{1}","lines":[26,29],"size":149,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#漏洞原理#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#漏洞原理#{2}","lines":[30,31],"size":36,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#漏洞原理#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#漏洞原理#{3}","lines":[32,33],"size":33,"outlinks":[{"title":"#可利用协议","target":"#可利用协议","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#漏洞原理#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#漏洞原理#{4}","lines":[34,35],"size":30,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#漏洞原理#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#漏洞原理#{5}","lines":[36,36],"size":11,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#漏洞原理#file_get_contents() 与 readfile()函数的利用": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#漏洞原理#file_get_contents() 与 readfile()函数的利用","lines":[37,46],"size":249,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#漏洞原理#file_get_contents() 与 readfile()函数的利用#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#漏洞原理#file_get_contents() 与 readfile()函数的利用#{1}","lines":[38,46],"size":207,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#漏洞原理#file_get_contents() 与 readfile()函数的利用#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#漏洞原理#file_get_contents() 与 readfile()函数的利用#{2}","lines":[41,46],"size":89,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**file://**": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**file://**","lines":[49,55],"size":159,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**file://**#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**file://**#{1}","lines":[50,55],"size":144,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**file://**#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**file://**#{2}","lines":[52,55],"size":60,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**dict://**": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**dict://**","lines":[56,64],"size":226,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**dict://**#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**dict://**#{1}","lines":[57,57],"size":39,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**dict://**#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**dict://**#{2}","lines":[58,58],"size":35,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**dict://**#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**dict://**#{3}","lines":[59,64],"size":135,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**dict://**#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**dict://**#{4}","lines":[60,64],"size":92,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**SFTP://**": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**SFTP://**","lines":[65,72],"size":123,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**SFTP://**#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**SFTP://**#{1}","lines":[66,66],"size":25,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**SFTP://**#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**SFTP://**#{2}","lines":[67,72],"size":82,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**SFTP://**#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**SFTP://**#{3}","lines":[68,72],"size":44,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**TFTP://**": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**TFTP://**","lines":[73,77],"size":237,"outlinks":[{"title":"PHP","target":"PHP","line":3},{"title":"Trivial File Transfer Protocol","target":"TFTP","line":3},{"title":"TFTP","target":"TFTP","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**TFTP://**#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**TFTP://**#{1}","lines":[75,75],"size":97,"outlinks":[{"title":"PHP","target":"PHP","line":1},{"title":"Trivial File Transfer Protocol","target":"TFTP","line":1},{"title":"TFTP","target":"TFTP","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**TFTP://**#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**TFTP://**#{2}","lines":[76,77],"size":119,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**LDAP://**": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**LDAP://**","lines":[78,81],"size":158,"outlinks":[{"title":"轻量级目录访问协议","target":"LDAP协议","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**LDAP://**#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**LDAP://**#{1}","lines":[80,81],"size":137,"outlinks":[{"title":"轻量级目录访问协议","target":"LDAP协议","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**SMTP**": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**SMTP**","lines":[82,84],"size":89,"outlinks":[{"title":"SMTP协议","target":"SMTP协议","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**SMTP**#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md#可利用协议#**SMTP**#{1}","lines":[84,84],"size":72,"outlinks":[{"title":"SMTP协议","target":"SMTP协议","line":1}],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08525978,0.00806061,-0.01225721,-0.06242554,0.0182901,0.00267047,-0.01461847,-0.00348667,0.05255949,0.00885268,0.00212726,-0.06539,0.03660679,0.0980781,0.08651213,0.00575583,-0.00084037,-0.00952304,0.0018394,0.0369339,0.08280709,-0.0404039,-0.02487605,-0.06384072,-0.03018927,0.00209277,0.01233192,0.04077397,-0.03341515,-0.12514442,0.04713699,-0.02330243,-0.03903649,-0.00909988,-0.01736918,-0.06209103,0.05165775,0.01896015,-0.01403772,0.0118051,0.00755191,0.06027508,-0.00282187,-0.01409932,-0.03872139,-0.0892021,-0.03926839,-0.00382429,0.01186495,-0.02928067,-0.0220617,-0.04458049,-0.04341904,0.00757825,-0.0285989,-0.00530207,0.04055259,0.04545258,0.00950068,0.00137815,0.09092765,-0.02497565,-0.18780056,-0.00377592,0.02325508,-0.04888869,-0.01301162,-0.02834019,0.0601249,0.04837689,-0.09988585,0.06025599,-0.04985833,0.04623505,0.01308557,0.00008069,-0.00337343,0.00182114,-0.03108006,-0.06948966,-0.02647313,0.04432376,-0.00118387,0.03769343,-0.00545917,0.04427633,-0.01572345,0.01792881,0.0253156,0.05090852,-0.01237682,-0.0775076,0.03754419,0.02930242,0.00980014,0.03719856,0.01433213,0.10535364,-0.09492498,0.13650858,-0.04174354,-0.01068828,-0.011145,-0.0771498,-0.01855043,-0.03571646,0.00987333,-0.01777519,0.02779197,-0.0124657,-0.06112238,-0.01726409,0.00175297,-0.01258298,0.00026472,0.03250642,-0.01050314,0.02723337,-0.01688621,-0.04220483,-0.0704794,-0.00897356,0.07536254,-0.04672914,-0.01604696,-0.03111213,0.05019839,0.05890481,0.05247983,0.05783734,0.04625706,-0.00693549,-0.03735124,-0.05770452,-0.00781431,-0.03048128,-0.02097755,0.00433876,0.01293436,-0.03766312,-0.02519126,-0.10221173,-0.00955365,-0.08258548,-0.06283417,0.09383815,-0.03608275,-0.00490113,0.00181776,-0.0357466,0.02212053,0.09155915,-0.00133158,-0.00644729,-0.00770544,0.03694391,0.05113028,0.14522295,-0.02369999,-0.05099537,0.01381594,0.02444669,-0.06535814,0.15088084,0.05991868,-0.09844469,-0.06215612,0.01393045,0.00079547,0.00781176,0.00579721,-0.01997109,0.01260836,-0.02621266,0.03584101,-0.01429345,-0.00474292,-0.02225967,-0.00221631,0.02396041,0.0460519,-0.03395053,-0.05521735,0.05308391,-0.0246465,-0.12463149,-0.02421265,-0.0256219,0.00790994,-0.07752223,-0.12120847,0.01871621,-0.05906837,0.02252447,0.00331235,-0.0653341,0.03807434,0.01404145,0.02945172,-0.03739494,0.11907153,0.05146375,-0.02887053,-0.00929986,0.00024556,0.00277367,0.02841972,0.01735302,0.02557047,0.04935586,-0.01213602,-0.00089146,0.00210101,0.05178924,-0.01888981,-0.01809974,0.01226247,0.00868302,0.02519152,0.08251605,-0.04233804,-0.0158725,-0.07491446,-0.20792848,-0.05044106,-0.01429852,-0.02566565,0.04774646,-0.02417038,0.06081887,0.04315027,0.05533049,0.11219,0.1368072,0.03455448,-0.05116277,-0.00396619,0.02690167,-0.02057796,0.02255954,0.00525792,-0.01455356,-0.01524516,-0.020744,0.03480971,0.01504102,0.00643172,0.05889851,-0.01632068,0.13611273,0.02347141,-0.01052893,-0.01414846,0.0150135,0.03392402,0.01985616,-0.10703279,0.006306,0.02484725,-0.02312086,-0.02698204,-0.00429373,0.01845473,-0.00354889,0.03379665,-0.01577575,-0.07194185,-0.00426089,-0.02088277,-0.02709615,-0.03252302,-0.01162587,0.09627383,-0.02790746,-0.05063969,-0.02177014,0.03752881,0.01479462,-0.03887951,-0.0367992,-0.05438443,-0.00004201,0.04362784,0.04144514,0.00841852,0.02863369,-0.01569644,0.00925919,-0.0109915,-0.05936975,-0.00011354,-0.00492996,-0.02690873,-0.07384604,0.17449647,-0.01042456,0.00444826,0.04388061,0.02299576,0.02466081,-0.0494552,0.01130212,0.0076965,0.05826976,-0.00377198,0.02226218,-0.04441018,-0.00599624,0.03672016,-0.01731025,-0.00631853,0.07894936,-0.03036842,-0.02929053,-0.04643982,-0.09549802,0.00755871,0.06747486,-0.00455506,-0.28272972,0.05018274,-0.03497419,0.02454902,-0.01505153,0.00403917,0.04711204,0.0314411,-0.06326518,0.04428567,-0.03244711,0.07131267,0.01421606,-0.0713245,-0.00217131,-0.01652422,0.01556693,0.00520352,0.0644242,-0.03418056,0.03568074,0.0329428,0.20675778,0.04000929,0.0554422,0.00198579,0.00313841,-0.0107019,0.06239041,0.00771138,0.01431961,-0.06752192,0.06912325,-0.0290051,0.06183515,0.03219765,-0.04094448,-0.01251551,0.02420203,-0.00998234,-0.05283969,0.04404725,-0.04313838,0.08809473,0.07607096,0.03261692,-0.00274489,-0.02455495,0.02382268,0.01603395,0.00418004,0.03507909,-0.02117582,-0.02456565,0.01056075,0.03561786,0.00084578,-0.03493423,-0.03904374,0.04278769,0.00646589,-0.03961619,0.08927547,0.08739471,0.01742725],"last_embed":{"hash":"5281f7cad25de228a432abad5292a4ad5b43bbbf2712026019237b9d00a20848","tokens":487}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.03979107,-0.01461233,0.00791229,-0.04765332,0.00391682,0.02533163,0.03911385,-0.00972745,-0.04093221,-0.03034964,0.00513431,-0.01997904,-0.03730615,-0.04524745,0.04371992,0.05014747,-0.01352094,-0.02805778,0.08887137,0.01018745,-0.01379654,0.0368425,-0.00328235,0.01818953,-0.009697,-0.04636801,-0.01491084,-0.05937427,-0.0060242,0.04785111,0.07742021,0.01213673,0.02277542,-0.0112316,-0.06227886,-0.00324208,0.01501867,-0.00517115,-0.00672177,-0.04297245,-0.01766233,0.01157507,-0.0653811,0.04477818,-0.06514655,0.02184735,-0.00650458,-0.04155248,0.02327665,0.00296972,0.02618157,-0.0151077,0.01109839,0.02630167,-0.00238894,0.05611429,0.0260618,-0.06348795,-0.01336727,-0.03060623,0.01046813,0.03504707,0.06642643,0.03282272,0.02437971,0.01960467,0.00292599,0.01857508,0.02187081,-0.02388216,-0.07227582,-0.01690145,0.0478405,0.03173424,0.01548354,-0.00794154,0.00651786,-0.00192664,-0.04283241,0.02260745,-0.05307222,0.05367373,-0.02327264,0.11093701,0.00440175,-0.03646854,0.00607267,-0.07401875,0.01265072,0.00628743,0.00553066,0.03515732,-0.02892373,-0.0130165,0.04387816,-0.01733477,-0.01328593,0.02329188,0.00572319,-0.04530211,0.05812199,0.03015735,-0.03323164,0.00176558,-0.07283897,-0.07774065,-0.0464046,-0.01941178,-0.01342801,-0.01154611,-0.04298092,-0.00067913,-0.05428074,-0.03577168,-0.02057309,0.03525095,-0.03076178,-0.03394688,0.00513374,0.03622294,0.00778203,0.04157953,-0.02722008,-0.0070785,-0.11426109,0.01031501,-0.00684514,-0.04343976,0.00205555,-0.00175487,-0.04089535,0.08082231,-0.02380501,-0.02405359,0.03175262,-0.02065173,0.06957687,-0.06956892,0.02445478,-0.01200738,0.00600633,-0.02152293,-0.03972424,-0.00677613,-0.0175122,0.05375585,-0.07450306,-0.03273426,-0.0017567,0.03367186,0.02779282,-0.01255213,-0.05243901,-0.03800456,-0.01425101,0.00415714,0.05414092,0.06805339,0.02190269,-0.01035898,-0.00992672,0.01321331,-0.00737727,-0.01393566,0.00505368,0.00135091,-0.05910098,0.03363093,0.02126699,-0.04402297,0.03283217,0.00440643,0.0018632,0.00408564,0.02896824,-0.0105182,0.06927873,0.00844241,-0.05637243,0.00227315,0.03624111,0.03921805,0.00940128,0.03001996,-0.00149892,0.03648756,-0.02515886,0.04914628,0.07308536,0.00688048,0.04399516,0.03102059,0.0352242,0.01278168,0.04288423,0.03801668,-0.02294192,-0.07434282,0.01098658,0.02850038,-0.01215985,-0.03337089,-0.05858673,-0.09585375,0.05326136,-0.02590304,-0.02735861,-0.01549017,-0.06582692,0.0599791,0.00369242,-0.00629448,0.03316479,0.02206175,-0.03611935,0.01867274,-0.02748586,0.06393321,0.03669067,-0.01658286,-0.003863,-0.01610845,-0.07647201,0.00405232,-0.01603108,-0.06032194,-0.00549451,-0.03111157,-0.00343436,0.03314297,-0.00430543,0.03577645,-0.00644296,0.00597486,-0.0424966,-0.01524009,0.04994713,0.04706991,-0.00531928,0.01356946,0.02461914,0.0059361,0.00445774,-0.01738908,-0.02138831,-0.03235282,-0.02507527,0.05393168,0.00878491,0.00871805,0.05401577,0.03705753,0.03705921,0.05202341,0.00336303,-0.06035627,-0.04307374,0.0098376,-0.00859251,-0.04945508,0.03601485,-0.04498402,-0.01912777,0.03891635,0.00509758,-0.00914829,0.01223142,0.00918928,-0.01281401,0.02654598,0.0299963,-0.04564223,0.05687572,0.03249663,0.04272104,-0.04264915,-0.00284748,-0.02908405,-0.01078017,-0.02260775,0.01061186,-0.05011302,0.03219029,0.01885383,-0.03767987,0.07377777,-0.00637549,-0.00829806,0.03543444,-0.00648673,-0.00717585,0.00817006,-0.0500109,0.05614178,-0.07307816,-0.0593679,0.0066836,0.0075595,0.01257527,-0.03246669,0.0344492,0.05398696,-0.00934546,-0.05100221,0.01208404,0.02510877,0.00379386,-0.00121787,0.00768056,-0.05067025,-0.00814903,-0.04979949,0.0100188,-0.02936392,0.00086609,0.09124859,0.03528321,-0.02002667,-0.00371444,-0.01761561,-0.0583319,0.01797336,-0.01811688,0.01969682,0.05694781,-0.11515436,0.0263522,0.00300679,0.00149622,0.01700385,-0.08032004,0.02826513,-0.02276159,-0.02443678,-0.00842221,-0.00757378,-0.0141023,-0.0638862,0.04818767,-0.05399641,0.04441082,-0.06131814,0.02246502,-0.01269223,0.01116815,0.04301818,-0.01707272,0.02671961,-0.03219767,-0.04188611,0.02631635,-0.01609477,-0.05068406,0.04407508,0.0190757,0.00247911,-0.06938034,-0.00449603,-0.04000404,0.00538941,-0.02463266,-0.03293852,-0.03242506,-0.03980799,-0.02141778,0.01176907,0.04058778,0.05102063,-0.05418144,-0.05964212,-0.01311882,-0.00147796,-0.05963797,-0.01601692,0.02540991,-0.00723651,-0.03289192,0.0413259,0.05257193,-0.01475058,-0.02203041,-0.07639163,0.0026334,-0.00806181,-0.05099676,-0.05421926,-0.02181034,-0.00530603,-0.03088989,-0.07850785,-0.00695845,0.03806638,-0.04208101,-0.02037746,0.02754971,0.02184721,0.05403084,-0.00284194,0.02139429,0.00094481,0.02620482,-0.01961073,-0.02387542,0.01113945,-0.02128721,0.02287722,-0.06351577,0.01961669,-0.02438608,-0.01043659,-0.02074388,0.02655949,-0.01202676,0.02340226,0.01457597,0.04642432,0.030398,-0.07609339,0.0052766,-0.01807042,0.03902896,-0.00892766,0.03865993,0.09211759,0.00532173,0.01192915,-0.08093012,-0.01073098,-0.02053729,-0.00314335,-0.07142912,-0.03607007,-0.09591552,-0.04202023,-0.02034058,-0.00969708,0.0348582,-0.01224965,0.02038217,-0.01076884,-0.0776191,-0.03068468,-0.01738734,-0.00358483,0.03963833,0.02417442,0.00530165,-0.0518779,0.00650867,0.05032601,-0.00414145,-0.06293106,-0.02101973,-0.01386549,0.0293257,0.00491974,0.0199224,-0.0426113,0.01182401,0.01502085,-0.05396437,-0.0177856,0.02928633,0.01761409,0.00837822,-0.00472152,0.01744596,0.04397572,0.00550097,-0.00696597,-0.01620337,-0.01504433,0.02402423,-0.03439789,0.05177812,0.03030087,0.00416129,-0.00295362,-0.04866624,-0.01453511,-0.00157735,0.03980543,-0.01713653,0.00248367,-0.04759794,0.02631559,-0.03122109,0.03024523,-0.02880656,-0.04362751,-0.03843217,0.08241326,0.02574925,0.01991192,-0.00549343,0.02468562,0.05825511,0.04634136,-0.0091085,0.06699201,0.00791378,0.02811211,-0.02165557,0.00267528,0.06640016,0.00566278,-0.03477693,0.02608969,0.04564228,-0.061818,-0.01738546,-0.00043351,0.03776049,0.01757971,0.0264032,-0.00546037,-0.04400424,-0.02731179,0.00020357,-0.02242473,-0.02022716,0.01765276,-0.06022902,0.00258056,0.01763387,0.00629961,0.01438256,0.07780663,0.01495816,0.01231252,0.00646598,0.01939701,0.03594249,-0.0504835,0.00434629,-0.01524416,0.03322358,-0.02471576,-0.01411445,0.04346877,0.06848561,-0.0093033,-0.0228247,0.02934047,-0.02044615,-0.03677269,0.06084031,-0.007021,0.05021099,0.00750437,0.00890191,-0.01457561,-0.0036505,-0.01391283,-0.02573465,0.02172603,0.00658105,-0.0246096,-0.01649156,0.11266628,0.03900559,0.04305831,0.02238,0.02314952,-0.01723524,-0.0145406,-0.02667111,-0.00928338,0.04438349,0.02507963,-0.09040571,0.01091788,0.03668655,-0.00622614,-0.0844312,0.02506186,0.01873926,0.0038635,0.04956161,0.01928809,0.02438638,-0.00256123,0.11798542,-0.00345212,-0.01644643,0.00080484,-0.01823929,0.04408057,0.00350927,-0.00829453,0.04949768,-0.02526408,-0.04015723,-0.01124321,-0.00712285,0.0220721,-0.03228636,0.0404133,0.01864256,-0.02817139,0.01139736,0.00350576,-0.03781504,0.02279719,0.00213276,-0.06899265,-0.00382056,-0.0725458,0.01202308,0.01009113,-0.00379688,0.07373502,-0.00187202,0.01101285,0.02023134,-0.06459234,-0.03922022,-0.01024213,-0.01503564,-0.01371641,-0.03829067,-0.03726713,-0.00650198,-0.00235287,0.0080931,0.0590343,0.010215,0.00334673,0.02229586,-0.05195491,-0.0472951,0.00869399,0.00852026,0.04570334,0.02217157,0.03545816,-0.04293617,-0.04541911,-0.0127719,-0.02499671,0.01385603,0.01170352,0.01616317,-0.00624313,0.01455147,-0.00154665,-0.07653799,-0.02236659,0.00767149,0.00201807,0.05050334,-0.02116796,0.01798532,0.00601872,0.00035909,0.03885892,0.00629491,0.00818536,0.01058602,0.0654082,-0.02456583,-0.02418988,0.02216298,0.05312772,-0.05965918,0.01883439,0.0525368,-0.00488639,-0.03786517,0.03246004,-0.03973984,-0.04272469,-0.00954456,0.02125813,-0.01306897,0.02397096,0.02527325,0.02501725,-0.00549193,-0.02988189,-0.08092791,-0.04556955,0.04842936,0.01641289,0.05949001,0.03116439,-0.01653366,0.01855981,-0.01170346,0.00122206,-0.09216993,-0.00302714,-0.01288322,-0.00472354,0.04033687,-0.03986805,0.01005578,0.00042111,-0.02382652,0.03924337,-0.03701217,-0.00789797,-0.04171325,0.05165618,-0.0268673,-0.03095458,0.04955327,0.04491153,0.02720829,0.00021135,0.00501972,-0.03812275,0.01121767,-0.04353633,0.03335707,-0.06064076,0.01849599,0.0448252,-0.07588733,-0.03190082,-0.00583749,0.02757474,0.04114337,0.07823505,-0.02959993,-0.03770797,-0.06648762,0.02047038,-0.01122515,0.02233966,-0.04143935,-0.05167983,0.02705685,0.07613575,0.04146487,-0.01030739,-0.01663135,0.00186851,0.07620938,-0.02221574,-0.01998677,-0.01398769,-0.01265493,-0.04621167,-0.04377434,0.03441764,-0.01986556,0.02875717,0.00758587,0.03377894,-0.05461366,-0.03384584,-0.00401379,0.08331498,0.0344036,-0.01751676,0.01148356,-0.03421536,0.00000107,0.00627971,0.02512019,0.05036318,-0.01791815,-0.02809982,-0.04231286,-0.06108574,0.03782282,-0.00156874],"last_embed":{"tokens":916,"hash":"11pqm3f"}}},"last_read":{"hash":"11pqm3f","at":1751441877263},"class_name":"SmartSource","outlinks":[{"title":"500","target":"Pasted image 20240709172620.png","line":21},{"title":"#可利用协议","target":"#可利用协议","line":32},{"title":"PHP","target":"PHP","line":75},{"title":"Trivial File Transfer Protocol","target":"TFTP","line":75},{"title":"TFTP","target":"TFTP","line":75},{"title":"轻量级目录访问协议","target":"LDAP协议","line":80},{"title":"SMTP协议","target":"SMTP协议","line":84}],"metadata":{"aliases":["Server-Side Request Forgery","服务器端请求伪造"],"英文":"Server-Side Request Forgery","工具界面":["命令行","GUI界面"]},"blocks":{"#---frontmatter---":[1,9],"#简介":[10,24],"#简介#{1}":[11,12],"#简介#{2}":[13,14],"#简介#{3}":[15,24],"#漏洞原理":[25,46],"#漏洞原理#{1}":[26,29],"#漏洞原理#{2}":[30,31],"#漏洞原理#{3}":[32,33],"#漏洞原理#{4}":[34,35],"#漏洞原理#{5}":[36,36],"#漏洞原理#file_get_contents() 与 readfile()函数的利用":[37,46],"#漏洞原理#file_get_contents() 与 readfile()函数的利用#{1}":[38,46],"#漏洞原理#file_get_contents() 与 readfile()函数的利用#{2}":[41,46],"#可利用协议":[47,84],"#可利用协议#**file://**":[49,55],"#可利用协议#**file://**#{1}":[50,55],"#可利用协议#**file://**#{2}":[52,55],"#可利用协议#**dict://**":[56,64],"#可利用协议#**dict://**#{1}":[57,57],"#可利用协议#**dict://**#{2}":[58,58],"#可利用协议#**dict://**#{3}":[59,64],"#可利用协议#**dict://**#{4}":[60,64],"#可利用协议#**SFTP://**":[65,72],"#可利用协议#**SFTP://**#{1}":[66,66],"#可利用协议#**SFTP://**#{2}":[67,72],"#可利用协议#**SFTP://**#{3}":[68,72],"#可利用协议#**TFTP://**":[73,77],"#可利用协议#**TFTP://**#{1}":[75,75],"#可利用协议#**TFTP://**#{2}":[76,77],"#可利用协议#**LDAP://**":[78,81],"#可利用协议#**LDAP://**#{1}":[80,81],"#可利用协议#**SMTP**":[82,84],"#可利用协议#**SMTP**#{1}":[84,84]},"last_import":{"mtime":1731306039781,"size":3556,"at":1748488128975,"hash":"11pqm3f"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/SSRF漏洞.md","last_embed":{"hash":"11pqm3f","at":1751441877263}},